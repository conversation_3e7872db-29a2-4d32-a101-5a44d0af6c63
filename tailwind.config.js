/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      fontFamily: {
        inter: ['Inter', 'sans-serif'],
        outfit: ['Outfit', 'sans-serif'],
      },

      colors: {
        primaryBorder: 'var(--primary-border)',
        sidebarBgColor: 'var(--sidebar-bg-color)',
        inputBgColor: 'var(--input-bg-color)',
        placeholderColor: 'var(-placeholder-color)',
        secondaryBtnBg: 'var(--secondary-btn-bg-color)',
        heroBannerBorder: 'var(--hero-border-color)',

        primary: {
          500: 'var(--primary-500)',
          700: 'var(--primary-700)',
          800: 'var(--primary-800)',
          900: 'var(--primary-900)',
          1000: 'var(--primary-1000)',
        },



        cetaceanBlue: {
          1000: 'var(--cetaceanBlue-1000)',
          2000: 'var(--color-1000)',
          3000: 'var(--color-2000)',
          4000: 'var(--steelTeal-200)'
        },

        cetaceanBlue2: {
          2000: 'var(--cetaceanBlue-2000)',
        },

        maastrichtBlue: {
          200: 'var(--maastrichtBlue-200)',
          500: 'var(--maastrichtBlue-500)',
          1000: 'var(--maastrichtBlue-1000)',
        },

        oxfordBlue: {
          900: 'var(--oxfordBlue-900)',
          1000: 'var(--oxfordBlue-1000)',
        },

        steelTeal: {
          100: 'var(--steelTeal-100)',
          200: 'var(--steelTeal-200)',
          500: 'var(--steelTeal-500)',
          1000: 'var(--steelTeal-1000)',
        },

        richBlack: {
          200: 'var(--richBlack-200)',
          500: 'var(--richBlack-500)',
          700: 'var(--richBlack-700)',
          900: 'var(--richBlack-900)',
          1000: 'var(--richBlack-1000)',
          600: 'var(--richBlack-600)',
        },

        fluorescentBlue: {
          1000: 'var(--fluorescentBlue-1000)',
        },

        green: {
          500: 'var(--green-500)',
          900: 'var(--green-900)',
          1000: 'var(--green-1000)',
        },

        orange: {
          800: 'var(--orange-800)',
          1000: 'var(--orange-1000)',
        },

        black: {
          200: 'var(--black-200)',
          500: 'var(--black-500)',
          850: 'var(--black-850)',
          900: 'var(--black-900)',
          1000: 'var(--black-1000)',
        },

        white: {
          100: 'var(--white-100)',
          200: 'var(--white-200)',
          400: 'var(--white-400)',
          500: 'var(--white-500)',
          700: 'var(--white-700)',
          750: 'var(--white-750)',
          1000: 'var(--white-1000)',
        },

        tiber: {
          1000: 'var(--tiber-1000)',
        },

        scarlet: {
          700: 'var(--scarlet-700)',
          900: 'var(--scarlet-900)',
        },

        slateGray: {
          400: 'var(--slate-gray-400)',
          500: 'var(--slate-gray-500)',
          800: 'var(--slate-gray-800)',
        },

        red: {
          1000: 'var(--red-1000)',
        },

        darkBlue: {
          800: 'var(--dark-blue-800)',
        },

        golden: {
          500: 'var(--golden-500)',
        },

        borderColor: {
          100: 'var(--border-color-100)',
        },
      },

      spacing: {
        sidebarWidth: 'var(--sidebar-wdith)',
        headerHeight: 'var(--header-heigth)',
        containerWidth: 'var(--container-width)',
        chatWidth: 'var(--chat-width)',
      },

      borderRadius: {
        px_10: '.625rem',
      },

      backgroundImage: {
        // GRADIENT COLOR
        progressBg:
          'linear-gradient(0deg, #BB7733 0%, #C17F37 7%, #D19844 20.01%, #EABB58 33.01%, #EECE6C 47.02%, #F2DD7B 61.02%, #ECC764 82.03%, #EABB58 99.03%)',
        'nav-gradient':
          'linear-gradient(90deg, rgba(235, 190, 91, 0) 46.57%, rgba(192, 126, 54, 0.4) 100%)',
        'profile-nav-gradient':
          'linear-gradient((90deg,_rgba(182,_14,_1,_0.2)_46.57%,_rgba(182,_14,_1,_0.4)_100%),_#002235)',
        'select-hover':
          'linear-gradient(90deg, rgba(182, 14, 1, 0.2) 46.57%, rgba(182, 14, 1, 0.4) 100%)',
        vipClub:
          'linear-gradient(270deg, rgba(11, 29, 65, 0) 0%, #083082 100%)',
        snipWheelBackground:
          'linear-gradient(52deg, rgba(182, 14, 1, 1) 0%, rgba(0, 0, 0, 0.7847514005602241) 35%, rgba(182, 14, 1, 1) 100%)',
        walletBg:
          ' linear-gradient(90deg, rgba(235, 190, 91, 0) -7.77%, rgba(192, 126, 54, 0.5) 100%)',
        TintGoldGradient:
          'linear-gradient(180deg, #E2BD68 0%, #ECD782 50%, #B57F44 100%)',

        // BACKGROUND IMAGE
        'auth-bg-desktop': 'url("/assets/demo-image/auth-bg-desktop.jpg")',
        'auth-bg-mobile':
          'url("../assets/images/stock-images/auth-bg-mobile.png")',
        'header-mask-green':
          'url("../assets/images/svg-images/header-mask-green.svg")',
        'header-mask-orange':
          'url("../assets/images/svg-images/header-mask-orange.svg")',
        'checkbox-check': 'url("/check-icon.svg")',
        'profile-sidebar':
          'url("../assets/images/stock-images/profile-sidebar.png")',
        'vip-tier': 'url("/assets/image/png/vipTierBg.png")',
        'vip-Container-bg': 'url("/assets/image/png/vipContainerBg.png")',
        'heroBannerBg': 'url("/assets/image/jpg/hero-img.jpg")'
      },

      keyframes: {
        heroOneText: {
          '0%': { transform: 'translateY(100%) scale(0.8)' },
          '10%': { transform: 'translateY(0%) scale(1)' },
          '25%': { transform: 'translateY(0%) scale(1)' },
          '40%': { transform: 'translateY(0%) scale(1)' },
          '47.5%': { transform: 'translateY(100%) scale(0.8)' },
        },

        heroTwoText: {
          '50%': { transform: 'translateY(100%) scale(0.8)' },
          '60%': { transform: 'translateY(0%) scale(1)' },
          '75%': { transform: 'translateY(0%) scale(1)' },
          '90%': { transform: 'translateY(0%) scale(1)' },
          '100%': { transform: 'translateY(100%) scale(0.8)' },
        },

        whiteShadow: {
          '0%': { right: ' -2.5rem ' },
          '49%': { right: '110%' },
          '100%': { right: '-2.5rem' },
        },
      },

      animation: {
        heroOneText: 'heroOneText 3s linear 0s infinite ',
        heroTwoText: 'heroTwoText 3s linear 0s infinite',
        whiteShadow: 'whiteShadow 3s ease-out 1.2s infinite',
      },

      boxShadow: {
        header: '0px 10px 25px var(--primary-750)',
        container: '0px 4px 4px var(--black-250)',
        'chat-header': '0px 4px 4px var(--black-250)',
        'bottom-menu': '0px 4px 4px var(--black-250)',
        vipLevelShadow:
          ' rgba(182, 14, 1, 1.17) 0px -3px 49px 1px inset, rgba(182, 14, 1, 0.15) 0px 15px 16px 0px inset, rgba(182, 14, 1, 0.1) 1px -59px 8px 0px inset, rgba(182, 14, 1, 0.06) 0px 2px 1px, rgba(182, 14, 1, 0.09) 0px 4px 2px, rgba(182, 14, 1, 0.09) 0px 0px 0px, rgba(182, 14, 1, 0.09) 0px 0px 0px, rgba(182, 14, 1, 0.09) 0px 0px 0px',
        inputInsetShadow: 'inset 0 0 7px 1px rgb(0 0 0)',
        vipCardShasow: 'inset 0 0 35px 1px #E52227',
        vipCardShasowdisable: 'inset 0 0 35px 1px #5C5C5C',
      },

      dropShadow: {
        vipCoinShadow: '0 0 0.75rem var(--scarlet-900)',
      },
    },
    screens: {
      xxs: '450px',
      xs: '475px',
      sm: '640px',
      md: '768px',
      lg: '1024px',
      lgx: '1340px',
      xl: '1280px',
      xxl: '1536px',

      'max-xxl': { max: '1535.98px' },
      // => @media (max-width: 1535.98px) { ... }

      'max-xl': { max: '1279.98px' },
      // => @media (max-width: 1279.98px) { ... }

      'max-lg': { max: '1023.98px' },
      // => @media (max-width: 1023.98px) { ... }

      'max-md': { max: '767.98px' },
      // => @media (max-width: 767.98px) { ... }

      'max-sm': { max: '639.98px' },
      // => @media (max-width: 639.98px) { ... }

      'max-xs': { max: '474.98px' },
      // => @media (max-width: 474.98px) { ... }

      'max-xxs': { max: '449.98px' },
      // => @media (max-width: 474.98px) { ... }

      tabletView: { 'min': '1280px', 'max': '1520px' },

      desktop: { min: '1341px', max: '1400.98px' },

      uppertab: { min: '1280px', max: '1340.98px' },
    },

    transform: {
      scale3d: 'scale3d(1.2, 1.5, 1.5)', // Default value, you can customize it
    },
    // maxWidth: {
    //   profileSidebar: '275px',
    //   fullwidth:'100%',
    //   providerWidth:'94px',

    // },
  },
  plugins: [],
};
