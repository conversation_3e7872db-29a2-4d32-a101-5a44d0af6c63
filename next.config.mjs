/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    // dirs: ['src'],
    ignoreDuringBuilds: true,
  },
  images: {
    domains: [
      'images.unsplash.com',
      'alibaba-dev-active-storage.s3.amazonaws.com',
      'tentengaming-prod-storage.s3.amazonaws.com',
      'alibaba-stag-storage.s3.amazonaws.com',
    ],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
  },
  async rewrites() {
    // Only enable proxy when API_URL points to proxy
    if (process.env.NEXT_PUBLIC_API_URL === '/api/proxy') {
      return [
        {
          source: '/api/proxy/:path*',
          destination: 'https://dev-api.fansbets.com/api/v1/:path*',
        },
      ];
    }
    return [];
  },
};

export default nextConfig;
