/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    // dirs: ['src'],
    ignoreDuringBuilds: true,
  },
  images: {
    domains: [
      'images.unsplash.com',
      'alibaba-dev-active-storage.s3.amazonaws.com',
      'tentengaming-prod-storage.s3.amazonaws.com',
      'alibaba-stag-storage.s3.amazonaws.com',
    ],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
  },
  async rewrites() {
    // Only use proxy in local development
    if (process.env.NODE_ENV === 'development' &&
        (process.env.HOSTNAME === 'localhost' || !process.env.HOSTNAME)) {
      return [
        {
          source: '/api/proxy/:path*',
          destination: 'https://dev-api.fansbets.com/api/v1/:path*',
        },
      ];
    }
    return [];
  },
};

export default nextConfig;
