/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    // dirs: ['src'],
    ignoreDuringBuilds: true,
  },
  images: {
    domains: [
      'images.unsplash.com',
      'alibaba-dev-active-storage.s3.amazonaws.com',
      'tentengaming-prod-storage.s3.amazonaws.com',
      'alibaba-stag-storage.s3.amazonaws.com',
    ],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
  },

};

export default nextConfig;
