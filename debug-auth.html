<!DOCTYPE html>
<html>
<head>
    <title>Debug Auth</title>
</head>
<body>
    <h1>Debug Authentication</h1>
    <button onclick="testCookie()">Test Cookie</button>
    <button onclick="testAPI()">Test API</button>
    <button onclick="setCookie()">Set Test Cookie</button>
    <div id="output"></div>

    <script>
        function log(message) {
            document.getElementById('output').innerHTML += '<p>' + message + '</p>';
        }

        function testCookie() {
            // Test if we can read cookies
            const cookies = document.cookie;
            log('All cookies: ' + cookies);
            
            // Test specific cookie
            const accessToken = getCookie('accessToken');
            log('Access token: ' + accessToken);
        }

        function getCookie(name) {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) return parts.pop().split(';').shift();
            return null;
        }

        function setCookie() {
            // Set a test cookie
            document.cookie = "accessToken=test-token-123; path=/";
            log('Test cookie set');
        }

        async function testAPI() {
            try {
                const response = await fetch('https://dev-api.fansbets.com/api/v1/user/get-user', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'accesstoken': getCookie('accessToken') || 'no-token'
                    },
                    credentials: 'include'
                });
                
                const data = await response.json();
                log('API Response: ' + JSON.stringify(data, null, 2));
            } catch (error) {
                log('API Error: ' + error.message);
            }
        }
    </script>
</body>
</html>
