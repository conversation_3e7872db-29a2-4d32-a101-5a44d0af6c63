'use client';

import React, { useEffect, useState } from 'react';
import QRCode from 'react-qr-code';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import {
  useTwoFactorAuthentication,
  useTwoFactorVerifyOtp,
  useTwoFactorEnable,
} from '@/reactQuery/authQuery';
import CopyIcon from '@/assets/icons/Copy';
import MainLoader from '@/components/Common/Loader/MainLoader';
import useAuthStore from '@/store/useAuthStore';
import { toast } from 'react-hot-toast';

function TwoFactorPage() {
  const [authKey, setAuthKey] = useState(null);
  const [authUrl, setAuthUrl] = useState(null);
  const [otp, setOtp] = useState('');
  const [authEnable, setAuthEnable] = useState(false);
  const [isClicked, setIsClicked] = useState(false);
  const [isLoading, setLoader] = useState(false);

  const { userDetails } = useAuthStore((state) => state);

  useEffect(() => {
    if (userDetails) {
      setLoader(true);
      setAuthEnable(userDetails.authEnable);
      setLoader(false);
    }
  }, [userDetails]);

  const mutation = useTwoFactorAuthentication({
    onSuccess: (response) => {
      const newAuthUrl = response?.data?.result?.authUrl;
      setAuthKey(response.data.result.authSecret);
      setAuthUrl(newAuthUrl);
      setLoader(false);
    },
    onError: (error) => {
      console.error('Error:', error);
      setLoader(false);
      toast.error('Failed to fetch authentication details');
    },
  });

  const mutationVerify = useTwoFactorVerifyOtp({
    onSuccess: (response) => {
      setAuthEnable(response.data.authEnable);
      setOtp('');
      setLoader(false);
      toast.success(response.data.message);
    },
    onError: (error) => {
      console.error('Error:', error);
      setLoader(false);
      toast.error('Failed to verify OTP');
    },
  });

  const verifyOtp = () => {
    if (otp) {
      setLoader(true);
      mutationVerify.mutate({
        token: otp,
      });
    }
  };

  const mutationEnable = useTwoFactorEnable({
    onSuccess: (response) => {
      setAuthEnable(response.data.result.authEnable);
      setLoader(false);
      toast.success(response.data.message);
    },
    onError: (error) => {
      console.error('Error:', error);
      setLoader(false);
      toast.error('Failed to update 2FA status');
    },
  });

  useEffect(() => {
    if (!authEnable) {
      setLoader(true);
      mutation.mutate();
    }
  }, [authEnable]);

  const copyToClipboard = (text) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        console.log('Text copied:', text);
      })
      .catch((err) => {
        console.error('Failed to copy text:', err);
      });
    setIsClicked(true);
  };

  if (isLoading) {
    return <MainLoader className="w-32" />;
  }
  console.log('authUrl', authUrl);
  return (
    <div>
      <div>
        <h3 className="text-xl font-bold capitalize text-white-1000">
          Google Secret Key
        </h3>
      </div>

      {!authEnable ? (
        <div>
          <div className="mt-4 flex items-center gap-7 max-md:flex-col max-md:justify-center">
            <div className="basis-[56%] bg-transparent max-md:w-full max-md:basis-[100%]">
              <label className="mb-1 block text-base font-normal text-steelTeal-1000">
                (Must save this key to restore 2fa once you lost it)
              </label>
              <div className="text-white relative w-full rounded-md border border-borderColor-100 bg-maastrichtBlue-1000 text-base font-normal focus:border-borderColor-100">
                <input
                  value={authKey || ''}
                  type="text"
                  readOnly
                  placeholder="3F52NG2E2XYUQEX2"
                  className="text-white w-full rounded-md bg-transparent p-3 text-base font-normal"
                />
                <div onClick={() => copyToClipboard(authKey)}>
                  <CopyIcon
                    className={`${isClicked ? 'fill-white-1000' : 'fill-steelTeal-1000'} absolute right-4 top-1/2 h-5 w-5 -translate-y-1/2 `}
                  />
                </div>
              </div>
            </div>

            <div className="basis-[44%] bg-transparent">
              <label className="mb-1 block text-center text-base font-normal leading-none text-white-1000">
                Sweep the QR code with Google Authenticator
              </label>
              <div className="flex justify-center p-4">
                <div className="w-fit rounded-lg bg-maastrichtBlue-1000 p-3.5">
                  <QRCode
                    size={256}
                    style={{ height: 'auto', maxWidth: '100%', width: '100%' }}
                    value={authUrl || ''}
                    viewBox="0 0 256 256"
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="columns-1">
            <div className="mt-4 w-full ">
              <label className="text-white mb-1 block text-base font-normal">
                Two Factor Code
              </label>
              <div className="text-white relative w-full rounded-md">
                <input
                  value={otp}
                  onChange={(e) => setOtp(e.target.value)}
                  type="text"
                  placeholder=""
                  className="text-white w-full rounded-md border border-maastrichtBlue-1000 bg-maastrichtBlue-1000 p-3 text-base font-normal focus:border-borderColor-100"
                />
              </div>
            </div>
          </div>

          <div className="mt-[30px] flex justify-center gap-4 rounded-md border border-steelTeal-1000 p-4 max-sm:mt-6">
            <PrimaryButton type="submit" onClick={verifyOtp}>
              Enable 2FA
            </PrimaryButton>
          </div>
        </div>
      ) : (
        <div className="mt-[30px] flex justify-center gap-4 rounded-md border border-steelTeal-1000 p-4 max-sm:mt-6">
          <PrimaryButton
            type="submit"
            onClick={() => {
              setLoader(true);
              mutationEnable.mutate();
            }}
          >
            Disable 2FA
          </PrimaryButton>
        </div>
      )}
    </div>
  );
}

export default TwoFactorPage;
