'use client';

import React, { useEffect } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import Select from 'react-select';
import { toast } from 'react-hot-toast';
import DatePicker from 'react-datepicker';
import PrimaryButtonOutline from '@/components/Common/Button/PrimaryButtonOutline';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import {
  useCountriesQuery,
  // useStateQuery,
  useUpdateProfileMutation,
} from '@/reactQuery/authQuery';
import useAuthStore from '@/store/useAuthStore';
import CalendarHeartIcon from '@/assets/icons/Calendar-Heart';
import { updateProfileSchema } from '@/config/schema';
import { phoneOptions } from '@/config/general';
import {
  eighteenYearsAgo,
  validateNumberInput,
  validateTextInput,
} from '@/utils/helper';
import { useQueryClient } from '@tanstack/react-query';

function ProfilePage() {
  const { userDetails } = useAuthStore((state) => state);
  const { data: countriesData, isLoading: countriesLoading } =
    useCountriesQuery({});
  const getCountriesOptions = () => {
    return countriesLoading
      ? []
      : countriesData?.map((item) => ({
          countryId: item?.id,
          value: item?.code,
          label: item?.name,
        }));
    // return [
    //   {
    //     countryId: 104,
    //     value: 'MM',
    //     label: 'Myanmar',
    //   },
    // ];
  };
  const genderOption = [
    { label: 'Male', value: 'male' },
    { label: 'Female', value: 'female' },
  ];
  const defaultValues = {
    firstName: userDetails?.firstName,
    lastName: userDetails?.lastName,
    username: userDetails?.lastName,
    dateOfBirth: userDetails?.dateOfBirth,
    phoneCode: phoneOptions[0],
    phone: userDetails?.phone,
    address: userDetails?.address_1,
    city: userDetails?.addresses?.[0]?.city || '',
    gender: genderOption?.filter((val) => val?.value === userDetails?.gender),
    zipCode: userDetails?.addresses?.[0]?.zipCode,
    // state: userDetails?.state
    //   ? { value: userDetails?.state, label: userDetails?.state }
    //   : '',

    //     country: userDetails?.country
    // ? { value: countriesData?.filter(country => country.code === userDetails?.addresses?.[0]?.countryCode)?.[0]?.name, label: countriesData?.filter(country => country.code === userDetails?.addresses?.[0]?.countryCode)?.[0]?.code }
    //  : '',
    countryCode: userDetails?.addresses?.length
      ? {
          label: countriesData?.filter(
            (country) =>
              country.code === userDetails?.addresses?.[0]?.countryCode,
          )?.[0]?.name,
          value: countriesData?.filter(
            (country) =>
              country.code === userDetails?.addresses?.[0]?.countryCode,
          )?.[0]?.code,
        }
      : '',
  };

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm({
    resolver: yupResolver(updateProfileSchema),
    defaultValues,
  });

  // const { data: stateData, isLoading: stateLoading } = useStateQuery({
  //   countryCode: 'MM',
  // });
  // const getStateOptions = () => {
  //   return stateLoading
  //     ? []
  //     : stateData?.map((item) => ({
  //         value: item?.name,
  //         label: item?.name,
  //       }));
  // };

  useEffect(() => {
    if (userDetails) {
      const updatedValues = {
        firstName: userDetails?.firstName,
        lastName: userDetails?.lastName,
        username: userDetails?.username,
        gender: genderOption?.filter(
          (val) => val?.value === userDetails?.gender,
        ),
        zipCode: userDetails?.addresses?.[0]?.zipCode,
        // nickname: userDetails?.nickname,
        dateOfBirth: userDetails?.dateOfBirth,
        phoneCode:
          phoneOptions.find(
            (option) => option.value === userDetails?.phoneCode,
          ) || phoneOptions[0],
        phone: userDetails?.phone,
        address: userDetails?.addresses?.[0]?.address,
        city: userDetails?.addresses?.[0]?.city || '',
        // state: userDetails?.state
        //   ? getStateOptions()?.find(
        //       (option) => option?.label === userDetails?.state,
        //     )
        //   : '',
        countryCode: userDetails?.addresses
          ? {
              label: countriesData?.filter(
                (country) =>
                  country.code === userDetails?.addresses?.[0]?.countryCode,
              )?.[0]?.name,
              value: countriesData?.filter(
                (country) =>
                  country.code === userDetails?.addresses?.[0]?.countryCode,
              )?.[0]?.code,
            }
          : '',
      };
      reset(updatedValues);
    }
  }, [userDetails, reset, countriesData]);

  // watcher to watch onchange in form  field
  const watchCountry = watch('countryCode');
  const watchGender = watch('gender');
  const queryClient = useQueryClient();

  const updateUserDetails = useUpdateProfileMutation({
    onSuccess: (response) => {
      if (response?.data) toast.success('Your profile updated successfully!');
      queryClient.invalidateQueries(['USER_PROFILE']);
    },
    onError: (error) => {
      const message =
        error.response?.data?.errors?.[0]?.description ||
        'Something went wrong';
      toast.error(message);
    },
  });

  const onSubmit = (formValues) => {
    const data = {
      ...formValues,
      firstName: formValues?.firstName,
      lastName: formValues?.lastName,
      // state: formValues?.state?.value || '',
      countryCode: formValues?.countryCode?.value || '',
      // nickname: formValues?.nickname,
      city: formValues?.city || '',
      gender: formValues?.gender?.value || '',
      zipCode: formValues?.zipCode || '',
      // mobileNo: formValues?.phone,
      dateOfBirth: formValues?.dateOfBirth || '',
      phoneCode: formValues?.phoneCode?.value || '',
    };
    updateUserDetails.mutate(data);
  };

  return (
    <div>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid grid-cols-2 gap-6 max-sm:grid-cols-1 desktop:gap-4 uppertab:grid-cols-1 uppertab:gap-3">
          <div className="">
            <label className="mb-1 block text-base font-normal capitalize text-steelTeal-1000">
              First Name
            </label>
            <div className="text-white relative w-full rounded-md bg-maastrichtBlue-1000 font-normal">
              <Controller
                name="firstName"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    value={field.value || ''}
                    type="text"
                    placeholder="first-name"
                    className={`text-white w-full rounded-md border border-solid border-transparent bg-transparent p-[15.5px] text-base font-normal leading-none placeholder-steelTeal-1000 focus:border focus:border-solid focus:border-borderColor-100 ${errors.firstName ? 'border-red-500' : ''}`}
                    onChange={(e) => {
                      if (validateTextInput(e.target.value)) {
                        field.onChange(e);
                      }
                    }}
                  />
                )}
              />
            </div>
            {errors.firstName && (
              <p className="text-red-500">{errors.firstName.message}</p>
            )}
          </div>

          <div className="">
            <label className="mb-1 block text-base font-normal capitalize text-steelTeal-1000">
              Last Name
            </label>
            <div className="text-white relative w-full rounded-md bg-maastrichtBlue-1000 font-normal">
              <Controller
                name="lastName"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    value={field.value || ''}
                    type="text"
                    placeholder="last-name"
                    className={`text-white w-full rounded-md border border-solid border-transparent bg-transparent p-[15.5px] text-base font-normal leading-none placeholder-steelTeal-1000 focus:border focus:border-solid focus:border-borderColor-100 ${errors.lastName ? 'border-red-500' : ''}`}
                    onChange={(e) => {
                      if (validateTextInput(e.target.value)) {
                        field.onChange(e);
                      }
                    }}
                  />
                )}
              />
            </div>
            {errors.lastName && (
              <p className="text-red-500">{errors.lastName.message}</p>
            )}
          </div>

          <div className="">
            <label className="mb-1 block text-base font-normal capitalize text-steelTeal-1000">
              Username
            </label>
            <div className="text-white relative w-full rounded-md bg-maastrichtBlue-1000 font-normal">
              <Controller
                name="username"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    value={field.value || ''}
                    type="text"
                    disabled
                    placeholder="username"
                    className={`text-white w-full rounded-md border border-solid border-transparent bg-transparent p-[15.5px] text-base font-normal leading-none placeholder-steelTeal-1000 focus:border focus:border-solid focus:border-borderColor-100 ${errors.username ? 'border-red-500' : ''}`}
                    onChange={(e) => {
                      if (validateTextInput(e.target.value)) {
                        field.onChange(e);
                      }
                    }}
                  />
                )}
              />
            </div>
            {errors.username && (
              <p className="text-red-500">{errors.username.message}</p>
            )}
          </div>
          {/* 
          <div className="">
            <label className="mb-1 block text-base font-normal capitalize text-steelTeal-1000">
              Nickname
            </label>
            <div className="text-white relative w-full rounded-md bg-maastrichtBlue-1000 font-normal">
              <Controller
                name="nickname"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    value={field.value || ''}
                    type="text"
                    placeholder="nickname"
                    className={`text-white w-full rounded-md border border-solid border-transparent bg-transparent p-[15.5px] text-base font-normal leading-none placeholder-steelTeal-1000 focus:border focus:border-solid focus:border-borderColor-100 ${errors.nickname ? 'border-red-500' : ''}`}
                    onChange={(e) => {
                      if (validateTextInput(e.target.value)) {
                        field.onChange(e);
                      }
                    }}
                  />
                )}
              />
            </div>
            {errors.nickname && (
              <p className="text-red-500">{errors.nickname.message}</p>
            )}
          </div> */}

          <div className="">
            <label className="mb-1 block text-base font-normal capitalize text-steelTeal-1000">
              Date of Birth
            </label>
            <div />
            <div className="text-white relative w-full rounded-md bg-maastrichtBlue-1000 font-normal [&>.react-datepicker-wrapper]:w-full">
              <Controller
                name="dateOfBirth"
                control={control}
                render={({ field }) => (
                  <DatePicker
                    showYearDropdown
                    scrollableYearDropdown
                    yearDropdownItemNumber={105}
                    placeholderText="MM/DD/YYYY"
                    onChange={(date) =>
                      field.onChange(
                        date !== 'Invalid Date' && date !== null ? date : '',
                      )
                    }
                    selected={field.value}
                    utcOffset={0}
                    onKeyDown={(e) => {
                      e.preventDefault();
                    }}
                    maxDate={eighteenYearsAgo()}
                    className={`text-white w-full rounded-md border border-solid border-transparent bg-transparent p-[15.5px] text-base font-normal leading-none placeholder-steelTeal-1000 focus:border focus:border-solid focus:border-borderColor-100 ${errors.dateOfBirth ? 'border-red-500' : ''}`}
                  />
                )}
              />

              <div className="pointer-events-none absolute right-4 top-1/2 translate-y-[-50%]">
                <CalendarHeartIcon className="fill-steelTeal-1000" />
              </div>
            </div>
            {errors.dateOfBirth && (
              <p className="text-red-500">{errors.dateOfBirth.message}</p>
            )}
          </div>

          <div className="">
            <label className="mb-1 block text-base font-normal capitalize text-steelTeal-1000">
              Phone
            </label>
            <div className="flex gap-2">
              <div className="w-full max-w-[75px]">
                <Select
                  name="phoneCode"
                  className="mobile-input-select"
                  classNamePrefix="mobile-input"
                  placeholder="+XX"
                  onChange={(val) => setValue('phoneCode', val)}
                  options={phoneOptions}
                  defaultValue={defaultValues.phoneCode}
                />
              </div>
              <div className="text-white relative w-full rounded-md bg-maastrichtBlue-1000 font-normal">
                <Controller
                  name="phone"
                  control={control}
                  render={({ field }) => (
                    <input
                      {...field}
                      value={field.value || ''}
                      type="text"
                      placeholder="XX-XXXX-XXXX"
                      className={`text-white w-full rounded-md border border-solid border-transparent bg-transparent p-[15.5px] text-base font-normal leading-none placeholder-steelTeal-1000 focus:border focus:border-solid focus:border-borderColor-100 ${errors.phone ? 'border-red-500' : ''}`}
                      onChange={(e) => {
                        if (validateNumberInput(e.target.value)) {
                          field.onChange(e);
                        }
                      }}
                    />
                  )}
                />
              </div>
            </div>
            {errors.phone && (
              <p className="text-red-500">{errors.phone.message}</p>
            )}
          </div>

          <div className="">
            <label className="mb-1 block text-base font-normal capitalize text-steelTeal-1000">
              Address
            </label>
            <div className="text-white relative w-full rounded-md bg-maastrichtBlue-1000 font-normal">
              <Controller
                name="address"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    value={field.value || ''}
                    type="text"
                    placeholder="address"
                    className={`text-white w-full rounded-md border border-solid border-transparent bg-transparent p-[15.5px] text-base font-normal leading-none placeholder-steelTeal-1000 focus:border focus:border-solid focus:border-borderColor-100 ${errors.address ? 'border-red-500' : ''}`}
                  />
                )}
              />
            </div>
            {errors.address && (
              <p className="text-red-500">{errors.address.message}</p>
            )}
          </div>

          <div className="">
            <label className="mb-1 block text-base font-normal capitalize text-steelTeal-1000">
              City
            </label>
            <div className="text-white relative w-full rounded-md bg-maastrichtBlue-1000 font-normal">
              <Controller
                name="city"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    value={field.value || ''}
                    type="text"
                    placeholder="city"
                    maxLength={30}
                    className={`text-white w-full rounded-md border border-solid border-transparent bg-transparent p-[15.5px] text-base font-normal leading-none placeholder-steelTeal-1000 focus:border focus:border-solid focus:border-borderColor-100 ${errors.city ? 'border-red-500' : ''}`}
                  />
                )}
              />
            </div>
            {errors.city && (
              <p className="text-red-500">{errors.city.message}</p>
            )}
          </div>

          <div className="">
            <label className="mb-1 block text-base font-normal capitalize text-steelTeal-1000">
              Country
            </label>
            <div className="">
              <Select
                name="countryCode"
                className="form-input-select"
                classNamePrefix="form-input"
                placeholder="Select Country"
                onChange={(val) => {
                  console.log('🚀 ~ ProfilePage ~ val:', val);
                  setValue('countryCode', val);
                }}
                options={getCountriesOptions()}
                value={watchCountry}
              />
            </div>
            {errors.countryCode && (
              <p className="text-red-500">{errors.countryCode.message}</p>
            )}
          </div>

          {/* <div className="">
            <label className="mb-1 block text-base font-normal capitalize text-steelTeal-1000">
              State
            </label>
            <div className="">
              <Select
                name="state"
                className="form-input-select"
                classNamePrefix="form-input"
                placeholder="Select State"
                onChange={(val) => setValue('state', val)}
                // options={getStateOptions()}
                // value={watchState}
              />
            </div>
            {errors.state && (
              <p className="text-red-500">{errors.state.message}</p>
            )}
          </div> */}
          <div className="">
            <label className="mb-1 block text-base font-normal capitalize text-steelTeal-1000">
              Zip Code
            </label>
            <div className="text-white relative w-full rounded-md bg-maastrichtBlue-1000 font-normal">
              <Controller
                name="zipCode"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    value={field.value || ''}
                    type="text"
                    placeholder="Enter zip code"
                    maxLength={30}
                    className={`text-white w-full rounded-md border border-solid border-transparent bg-transparent p-[15.5px] text-base font-normal leading-none placeholder-steelTeal-1000 focus:border focus:border-solid focus:border-borderColor-100 ${errors.city ? 'border-red-500' : ''}`}
                  />
                )}
              />
            </div>
            {errors.zipCode && (
              <p className="text-red-500">{errors.zipCode.message}</p>
            )}
          </div>

          <div className="">
            <label className="mb-1 block text-base font-normal capitalize text-steelTeal-1000">
              Gender
            </label>
            <div className="">
              <Select
                name="gender"
                className="form-input-select"
                classNamePrefix="form-input"
                placeholder="Select Gender"
                onChange={(val) => setValue('gender', val)}
                options={genderOption}
                value={watchGender}
                isSearchable={false}
              />
            </div>
            {errors.gender && (
              <p className="text-red-500">{errors.gender.message}</p>
            )}
          </div>
        </div>

        <div className="mt-[30px] flex justify-center gap-4 rounded-md border border-steelTeal-1000 p-4 max-sm:mt-6">
          <PrimaryButton type="submit" isLoading={updateUserDetails?.isPending}>
            Save
          </PrimaryButton>
          <PrimaryButtonOutline
            type="button"
            onClick={() => reset(defaultValues)}
          >
            Cancel
          </PrimaryButtonOutline>
        </div>
      </form>
    </div>
  );
}

export default ProfilePage;
