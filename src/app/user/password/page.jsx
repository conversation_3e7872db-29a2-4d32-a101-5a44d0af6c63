'use client';

import React, { useState } from 'react';
import { toast } from 'react-hot-toast';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import PrimaryButtonOutline from '@/components/Common/Button/PrimaryButtonOutline';
import EyeOpenIcon from '@/assets/icons/Eye-Open';
import IconButton from '@/components/Common/Button/IconButton';
import { changePasswordSchema } from '@/schemas/auth';
import { useChangePasswordMutation } from '@/reactQuery/authQuery';
import { validateSpace } from '@/utils/helper';
import EyeCloseIcon from '@/assets/icons/Eye-Close';

function PasswordPage() {
  const [showTooltipnew, setShowTooltipNew] = useState(false);
  const [showPasswords, setShowPasswords] = useState({
    currentPassword: false,
    newPassword: false,
    confirmNewPassword: false,
  });

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm({
    resolver: yupResolver(changePasswordSchema),
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      confirmNewPassword: '',
    },
  });

  const mutation = useChangePasswordMutation({
    onSuccess: (response) => {
      toast.success('Password changed successfully');
      reset();
    },
    onError: (error) => {
      toast.error(error.response.data.errors.map((e) => e.description));
    },
  });

  const onSubmit = (formValues) => {
    if (formValues.currentPassword === formValues.newPassword) {
      toast.error('New Password is matching with old one');
    } else {
      mutation.mutate({
        currentPassword: btoa(formValues.currentPassword),
        newPassword: btoa(formValues.newPassword),
      });
    }
  };

  const handleCancel = () => {
    reset();
  };

  const togglePasswordVisibility = (field) => {
    setShowPasswords((prevShowPasswords) => ({
      ...prevShowPasswords,
      [field]: !prevShowPasswords[field],
    }));
  };

  return (
    <div>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid grid-cols-2 gap-6 max-sm:grid-cols-1 max-sm:pb-2 desktop:gap-4 uppertab:grid-cols-1 uppertab:gap-3">
          <div>
            <label className="text-white mb-1 flex flex-row gap-2 text-base font-normal">
              Current Password
            </label>
            <div className="text-white relative w-full rounded-md bg-maastrichtBlue-1000 text-base font-normal">
              <Controller
                name="currentPassword"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    type={showPasswords.currentPassword ? 'text' : 'password'}
                    placeholder="Please enter a current password"
                    className={`text-white w-full rounded-md bg-transparent p-3 pr-12 text-base font-normal ${errors.currentPassword ? 'border border-solid border-primary-1000' : 'border border-solid border-borderColor-100'}`}
                    onChange={(e) => {
                      if (!validateSpace(e.target.value)) {
                        field.onChange(e);
                      }
                    }}
                  />
                )}
              />
              <IconButton className="absolute right-2 top-1/2 -translate-y-1/2">
                {showPasswords.currentPassword ? (
                  <EyeOpenIcon
                    className="h-5 w-5 cursor-pointer fill-steelTeal-1000"
                    onClick={() => togglePasswordVisibility('currentPassword')}
                    alt="Toggle visibility"
                  />
                ) : (
                  <EyeCloseIcon
                    className="h-5 w-5 cursor-pointer fill-steelTeal-1000"
                    onClick={() => togglePasswordVisibility('currentPassword')}
                    alt="Toggle visibility"
                  />
                )}
              </IconButton>
            </div>
            {errors.currentPassword && (
              <p className="mt-1 text-xs text-red-500">
                {errors.currentPassword.message}
              </p>
            )}
          </div>
        </div>

        <div className="mt-4 grid grid-cols-2 gap-6 max-sm:grid-cols-1 desktop:gap-4 uppertab:grid-cols-1 uppertab:gap-3">
          <div>
            <label className="mb-1 flex flex-row gap-2 text-base font-normal text-steelTeal-1000 relative">
              New Password
              <label
                className="flex h-5 w-5 items-center justify-center rounded-full border bg-gray-700 text-white-1000"
                onMouseEnter={() => setShowTooltipNew(true)}
                onMouseLeave={() => setShowTooltipNew(false)}
              >
                i
                {showTooltipnew && (
                  <div className="text-white absolute bottom-full left-0 max-w-[295px] w-full rounded bg-gray-700 px-3 py-0.5 text-sm opacity-90">
                      Password must be 8-32 characters long and include at least one uppercase letter, one lowercase letter, one number, and one special character
                  </div>
                )}
              </label>
            </label>
            <div className="relative w-full">
              <Controller
                name="newPassword"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    type={showPasswords.newPassword ? 'text' : 'password'}
                    placeholder="Please enter a new password"
                    className={`text-white w-full rounded-md bg-maastrichtBlue-1000 p-[11px_12px] pr-12 text-base font-normal ${errors.newPassword ? 'border border-solid border-primary-1000' : 'border border-solid border-maastrichtBlue-1000'} focus:border focus:border-solid focus:border-borderColor-100`}
                    onChange={(e) => {
                      if (!validateSpace(e.target.value)) {
                        field.onChange(e);
                      }
                    }}
                  />
                )}
              />
              <IconButton className="absolute right-2 top-1/2 -translate-y-1/2">
                {showPasswords.newPassword ? (
                  <EyeOpenIcon
                    className="h-5 w-5 cursor-pointer fill-steelTeal-1000"
                    onClick={() => togglePasswordVisibility('newPassword')}
                    alt="Toggle visibility"
                  />
                ) : (
                  <EyeCloseIcon
                    className="h-5 w-5 cursor-pointer fill-steelTeal-1000"
                    onClick={() => togglePasswordVisibility('newPassword')}
                    alt="Toggle visibility"
                  />
                )}
              </IconButton>
            </div>
            {errors.newPassword && (
              <p className="mt-1 text-xs text-red-500">
                {errors.newPassword.message}
              </p>
            )}
          </div>

          <div>
            <label className="mb-1 block flex flex-row gap-2 text-base font-normal text-steelTeal-1000">
              Confirm New Password
            </label>
            <div className="relative w-full">
              <Controller
                name="confirmNewPassword"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    type={
                      showPasswords.confirmNewPassword ? 'text' : 'password'
                    }
                    placeholder="Please confirm your new password"
                    className={`text-white w-full rounded-md bg-maastrichtBlue-1000 p-[11px_12px] pr-12 text-base font-normal ${errors.confirmNewPassword ? 'border border-solid border-primary-1000' : 'border border-solid border-maastrichtBlue-1000'} focus:border focus:border-solid focus:border-borderColor-100`}
                    onChange={(e) => {
                      if (!validateSpace(e.target.value)) {
                        field.onChange(e);
                      }
                    }}
                  />
                )}
              />
              <IconButton className="absolute right-2 top-1/2 -translate-y-1/2">
                {showPasswords.confirmNewPassword ? (
                  <EyeOpenIcon
                    className="h-5 w-5 cursor-pointer fill-steelTeal-1000"
                    onClick={() =>
                      togglePasswordVisibility('confirmNewPassword')
                    }
                    alt="Toggle visibility"
                  />
                ) : (
                  <EyeCloseIcon
                    className="h-5 w-5 cursor-pointer fill-steelTeal-1000"
                    onClick={() =>
                      togglePasswordVisibility('confirmNewPassword')
                    }
                    alt="Toggle visibility"
                  />
                )}
              </IconButton>
            </div>
            {errors.confirmNewPassword && (
              <p className="mt-1 text-xs text-red-500">
                {errors.confirmNewPassword.message}
              </p>
            )}
          </div>
        </div>
        <div className="mt-[30px] flex justify-center gap-4 rounded-md border border-steelTeal-1000 p-4 max-sm:mt-6">
          <PrimaryButton type="submit">Save Password</PrimaryButton>
          <PrimaryButtonOutline type="button" onClick={handleCancel}>
            Cancel
          </PrimaryButtonOutline>
        </div>
      </form>
    </div>
  );
}

export default PasswordPage;
