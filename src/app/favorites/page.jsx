'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { useGetFavoritesGamesQuery } from '@/reactQuery/gamesQuery';
import useAuthStore from '@/store/useAuthStore';
import GameCard from '@/components/Common/GameCard';
import MainLoader from '@/components/Common/Loader/MainLoader';

function FavoritesPage() {
  const router = useRouter();
  const { isAuthenticated } = useAuthStore((state) => state);
  const { data: favoritesGames, isLoading } = useGetFavoritesGamesQuery({
    enabled: isAuthenticated,
  });

  return isLoading ? (
    <MainLoader className="w-32" />
  ) : favoritesGames?.rows?.length > 0 ? (
    <section className="mb-10 rounded-lg bg-oxfordBlue-1000 p-3 shadow-container">
      <div className="mb-4 flex items-center justify-between gap-4">
        <h6 className="text-xl font-bold text-white-1000">Favorites</h6>
      </div>
      <div className="grid grid-cols-2 gap-4 xs:grid-cols-3 md:grid-cols-5">
        {favoritesGames?.rows?.map((game, index) => (
          <GameCard
            key={game?.casinoGame?.name}
            src={game?.casinoGame?.iconUrl}
            alt={game?.name}
            gameId={game?.casinoGame?.id}
            isFavorite={true}
            width="10000"
            height="10000"
            onClick={() => router.push(`/game/${game?.casinoGame?.id}`)}
          />
        ))}
      </div>
    </section>
  ) : (
    <div className="flex h-96 items-center justify-center">
      No games are available
    </div>
  );
}

export default FavoritesPage;
