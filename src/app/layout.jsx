import { Inter } from 'next/font/google';
import { Toaster } from 'react-hot-toast';
import Script from 'next/script';
import { GoogleOAuthProvider } from '@react-oauth/google';
import './globals.css';
import Head from 'next/head';
import { SkeletonTheme } from 'react-loading-skeleton';
import Header from '@/components/Header';
import SideMenu from '@/components/SideMenu';
import ChatWindow from '@/components/ChatWindow';
import Footer from '@/components/Footer';
import { ReactQueryClientProvider } from '@/components/ReactQueryClientProvider/ReactQueryClientProvider';
import MainSection from '@/components/Common/MainSection';
import Modal from '@/components/Common/Modal/page';
import CallModal from '@/components/Common/Modal/CallModal';
// import Auth from '@/components/Auth';
import BottomMenu from '@/components/BottomMenu';

const inter = Inter({ subsets: ['latin'] });

export const metadata = {
  title: 'FansBets',
  description: 'online casino platform',
};
export const dynamic = 'force-dynamic';
export default function RootLayout({ children }) {
  return (
    <GoogleOAuthProvider
      clientId={process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || 'dsasdas'}
    >
      <html lang="en">
        <Head>
          <link rel="icon" href="/favicon.ico" />
        </Head>
        <body className={inter.className}>
          <div className="bg-black-1000">
            <ReactQueryClientProvider>
              <SkeletonTheme baseColor="rgb(5, 20, 56)" highlightColor="#444">

                <SideMenu />
                {/* <Auth /> */}
                <Toaster position="top-right" />
                <CallModal />
                <MainSection>
                <Header />
                {children}</MainSection>
                <Modal />
                <Footer />
                <ChatWindow />
                <BottomMenu />
                {/* <Notice /> */}
              </SkeletonTheme>
            </ReactQueryClientProvider>
          </div>
          <Script src="/pixi-legacy.min.js" />
        </body>
      </html>
    </GoogleOAuthProvider>
  );
}
