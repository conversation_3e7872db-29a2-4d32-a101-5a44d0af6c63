'use client';

import { useState } from 'react';
import { subDays } from 'date-fns';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { formatDate, formatDateMDY } from '@/utils/customizedDate';
import { useTransactions } from '@/hooks/useUserTransactions';
import TransactionFilter from '@/components/Filter';
import Loader from './components/Loader';
import Pagination from './components/Pagination';

const ITEMS_PER_PAGE = 10;
const INITIAL_DATE_RANGE = {
  start: formatDate(subDays(new Date(), 30)),
  end: formatDate(new Date()),
};

const options = [
  { value: null, label: 'All' },
  { value: 'CasinoWin', label: 'Casino Win' },
  { value: 'CasinoBet', label: 'Casino Bet' },
  { value: 'RollbackWin', label: 'Casino Rollback' },
];
const TableHeaders = [
  { id: 'transactionId', label: 'Transaction ID' },
  { id: 'gameName', label: 'Game Name' },
  { id: 'dateTime', label: 'Date & Time' },
  { id: 'amount', label: 'Amount' },
  { id: 'status', label: 'Status' },
  { id: 'type', label: 'Type' },
];

function DateRangeSelector({
  startDate,
  endDate,
  onDateChange,
  dateError,
  selectedOption,
  setSelectedOption,
}) {
  return (
    <div className="flex flex-col md:flex-row md:space-x-6 max-md:space-x-0 max-md:space-y-6">
      <DateInput
        selected={formatDate(startDate)}
        onChange={(date) => onDateChange(date, 'start')}
        maxDate={new Date()}
        // label="Start Date"
      />
      <DateInput
        selected={formatDate(endDate)}
        onChange={(date) => onDateChange(date, 'end')}
        maxDate={new Date()}
        // label="End Date"
        error={dateError}
      />
      <TransactionFilter
        selectedOption={selectedOption}
        setSelectedOption={setSelectedOption}
        options= {options}
      />
    </div>
  );
}

function DateInput({ selected, onChange, label, error, maxDate }) {
  return (
    <div className="max-w-[250px w-full]">
      <div className="flex flex-col ">
        {/* <label className="text-darkGray-1000 text-sm font-medium">
          {label}
        </label> */}
        <div className="relative">
          <DatePicker
            selected={selected}
            onSelect={onChange}
            maxDate={maxDate}
            showYearDropdown
            dateFormat="dd/MM/yyyy"
            aria-label={label}
            className="text-white w-full rounded-md border border-solid border-maastrichtBlue-1000  bg-maastrichtBlue-1000 p-[15.5px] text-base font-normal leading-none placeholder-steelTeal-1000 focus:border focus:border-solid focus:border-borderColor-100"
          />
          <div className="bg-blackOpacity-100 after:bg-borderBg absolute bottom-0 h-[3px] w-full after:absolute after:h-[3px] after:w-0 after:transition-all after:duration-300 after:ease-linear after:content-[''] peer-focus:after:w-full" />
        </div>
        {error && <p className="mt-1 text-[12px] text-red-500">{error}</p>}
      </div>
    </div>
  );
}

function TransactionTable({ transactions, isLoading }) {
  if (isLoading) return <Loader />;

  return (
    <table className="min-w-full divide-y-2 divide-richBlack-1000 bg-transparent">
      <thead>
        <tr>
          {TableHeaders.map(({ id, label }) => (
            <th
              key={id}
              className="text-primary-400 whitespace-nowrap px-4 py-3 text-start text-sm font-bold capitalize"
            >
              {label}
            </th>
          ))}
        </tr>
      </thead>
      <tbody className="divide-y divide-richBlack-1000 text-base">
        {transactions?.count > 0 ? (
          transactions.rows.map((transaction) => (
            <TransactionRow
              key={transaction.transactionId}
              transaction={transaction}
            />
          ))
        ) : (
          <tr>
            <td
              colSpan={TableHeaders.length}
              className="text-darkGray-1000 px-4 py-3 text-center font-normal"
            >
              No Record Found!
            </td>
          </tr>
        )}
      </tbody>
    </table>
  );
}

function TransactionRow({ transaction }) {
  return (
    <tr>
      <td className="text-darkGray-1000 whitespace-nowrap px-4 py-[9px] font-normal">
        {transaction?.transactionId || transaction?.id}
      </td>
      <td className="text-darkGray-1000 whitespace-nowrap px-4 py-[9px] font-normal">
        {transaction?.casinoGame?.name?.EN}
      </td>
      <td className="text-darkGray-1000 whitespace-nowrap px-4 py-[9px] font-normal">
        {formatDateMDY(transaction?.createdAt)}
      </td>
      <td className="text-darkGray-1000 whitespace-nowrap px-4 py-[9px] font-normal">
        $ {transaction?.ledger?.amount || 0}
      </td>
      <td className="text-warningRed-1000 whitespace-nowrap px-4 py-[9px] font-normal">
        {transaction?.status || 0}
      </td>
      <td className="text-warningRed-1000 whitespace-nowrap px-4 py-[9px] font-normal">
        {transaction?.ledger?.purpose}
      </td>
    </tr>
  );
}

function Transaction() {
  const [dateRange, setDateRange] = useState(INITIAL_DATE_RANGE);
  const [page, setPage] = useState(1);
  const [dateError, setDateError] = useState('');
  const [selectedOption, setSelectedOption] = useState(null);

  const { casinoTransactions, isLoading } = useTransactions({
    startDate: dateRange.start,
    endDate: dateRange.end,
    page,
    limit: ITEMS_PER_PAGE,
    purpose: selectedOption?.value
  });

  const handleDateChange = (date, type) => {
    if (type === 'start' && date >= dateRange.end) {
      setDateError('End date must be after start date.');
      setDateRange((prev) => ({ ...prev, end: new Date() }));
    } else if (type === 'end' && date.getTime() < dateRange.start.getTime()) {
      setDateError('End date must be after start date.');
    } else {
      setDateRange((prev) => ({ ...prev, [type]: date }));
      setDateError('');
    }
  };

  const totalPages = Math.ceil(
    (casinoTransactions?.count || 0) / ITEMS_PER_PAGE,
  );
  const showPagination = casinoTransactions?.count > 0 && totalPages > 1;

  const handlePageChange = (page) => {
    setPage(page);
  };

  return (
    <div className="rounded-xl border-2 border-solid border-maastrichtBlue-1000 bg-black-1000 px-5 py-[50px] max-xl:py-6 max-xxs:px-3">
      <DateRangeSelector
        startDate={dateRange.start}
        endDate={dateRange.end}
        onDateChange={handleDateChange}
        dateError={dateError}
        setSelectedOption={setSelectedOption}
        selectedOption={selectedOption}
      />
      <div className="mt-6">
        <div className="min-h-[100px] overflow-x-auto">
          <TransactionTable
            transactions={casinoTransactions}
            isLoading={isLoading}
          />
        </div>
        {showPagination && (
          <Pagination
            className="mt-4"
            totalPages={totalPages}
            currentPage={page}
            onPageChange={handlePageChange}
          />
        )}
      </div>
    </div>
  );
}

export default Transaction;
