import React from 'react';
import <PERSON>Arrow from '../../../../public/assets/svg/NextArrow';
import PrevArrow from '../../../../public/assets/svg/PrevArrow';



const Pagination = ({ currentPage, totalPages, onPageChange }) => {
  const generatePageNumbers = () => {
    const pages = [];
    const maxPagesToShow = 5; // Number of page buttons to display at once

    if (totalPages <= maxPagesToShow) {
      // If total pages are less than or equal to maxPagesToShow, display all pages
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Handle ellipsis and page range for large numbers of pages
      if (currentPage <= 3) {
        pages.push(1, 2, 3, 4, '...', totalPages);
      } else if (currentPage >= totalPages - 2) {
        pages.push(1, '...', totalPages - 3, totalPages - 2, totalPages - 1, totalPages);
      } else {
        pages.push(1, '...', currentPage - 1, currentPage, currentPage + 1, '...', totalPages);
      }
    }

    return pages;
  };

  const handlePageClick = (page) => {
    if (page !== '...') {
      onPageChange(page);
    }
  };

  return (
    <div className="flex justify-center gap-2 text-base font-medium mt-4">
      {/* Previous Button */}
      <button
        className={`size-8 rounded border text-center flex items-center justify-center ${currentPage === 1 ? 'cursor-not-allowed opacity-50' : ''
          }`}
        onClick={() => currentPage > 1 && onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
      >
        <PrevArrow />
      </button>

      {/* Dynamic Page Numbers */}
      {generatePageNumbers().map((page, index) => (
        <button
          key={index}
          className={`flex size-8 items-center justify-center rounded border text-center ${
            page === currentPage
              ? 'text-red border-red-500 bg-red-500'
              : 'border-whiteOpacity-100 text-whiteOpacity-200 bg-transparent'
          } ${page === '...' ? 'cursor-not-allowed' : ''}`}
          onClick={() => handlePageClick(page)}
        >
          {page}
        </button>
      ))}

      {/* Next Button */}
      <button
        className={`size-8 rounded border text-center flex items-center justify-center ${currentPage === totalPages ? 'cursor-not-allowed opacity-50' : ''
          }`}
        onClick={() => currentPage < totalPages && onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
      >
        <NextArrow />
      </button>
    </div>
  );
};

export default Pagination;
