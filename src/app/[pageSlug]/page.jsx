'use client';

import React from 'react';
import {
  useCmsPageDetailsQuery,
  useGetAllCmsPagesQuery,
} from '@/reactQuery/generalQueries';
import useAuthStore from '@/store/useAuthStore';
import ComingSoon from '@/components/ComingSoon';
import MainLoader from '@/components/Common/Loader/MainLoader';

function CMSPage({ params }) {
  const { pageSlug } = params;
  const { isAuthenticated } = useAuthStore((state) => state);

  const {
    data: cmsPageData,
    isLoading,
    error,
  } = useCmsPageDetailsQuery({
    enabled: isAuthenticated,
    pageSlug,
  });

  if (isLoading) {
    return <MainLoader className="w-32" />;
  }

  if (error) {
    return <ComingSoon />;
  }

  return (
    <div>
      <div
        className="content-para"
        dangerouslySetInnerHTML={{ __html: cmsPageData?.content?.EN }}
      />
    </div>
  );
}

export default CMSPage;
