/* eslint-disable no-shadow */
/* eslint-disable no-nested-ternary */

'use client';

import React, { useEffect, useState } from 'react';
import { useGameLaunchQuery } from '@/reactQuery/gamesQuery';
import useAuthStore from '@/store/useAuthStore';
import MainLoader from '@/components/Common/Loader/MainLoader';
import useGreenBonusStore from '@/store/useGreenBonusStore';
import { getAccessToken } from '@/utils/helper';
import { walletSocket } from '@/utils/socket';

function GamePage({ params }) {
  const { isAuthenticated, coin } = useAuthStore((state) => state);
  const accessToken = getAccessToken();
  const [animatedWidth, setAnimatedWidth] = useState('0%');

  const {
    isGreenBonusApplicable,
    betAmountToClaimBonus,
    totalBetAmountTill,
    setGreenBonusData,
    setTotalBetAmountTill,
  } = useGreenBonusStore((state) => ({
    isGreenBonusApplicable: state.isGreenBonusApplicable,
    betAmountToClaimBonus: state.betAmountToClaimBonus,
    totalBetAmountTill: state.totalBetAmountTill,
    setGreenBonusData: state.setGreenBonusData,
    setTotalBetAmountTill: state.setTotalBetAmountTill,
  }));

  const { data: gameDetails, isLoading } = useGameLaunchQuery({
    params: {
      coin,
      gameId: +params?.gameId,
    },
    enabled: isAuthenticated,
  });

  // State to control confetti
  const [showConfetti, setShowConfetti] = useState(false);
  // Function to create confetti
  const createConfetti = () => {
    const colors = ['#ff0', '#0f0', '#00f', '#f00', '#ff69b4']; // Customize colors
    const confettiCount = 100; // Number of confetti pieces

    for (let i = 0; i < confettiCount; i++) {
      const confetti = document.createElement('div');
      confetti.className = 'confetti';
      confetti.style.position = 'fixed';
      confetti.style.top = '-10px'; // Start above the screen
      confetti.style.left = `${Math.random() * 100}vw`; // Random x-position
      confetti.style.width = `${Math.random() * 10 + 5}px`; // Random width
      confetti.style.height = `${Math.random() * 10 + 5}px`; // Random height
      confetti.style.backgroundColor =
        colors[Math.floor(Math.random() * colors.length)]; // Random color
      confetti.style.opacity = '0.8';
      confetti.style.transform = `rotate(${Math.random() * 360}deg)`; // Random rotation
      confetti.style.zIndex = '1000'; // Ensure it’s on top

      // Animation
      confetti.animate(
        [
          { transform: `translateY(0) rotate(${Math.random() * 360}deg)` },
          {
            transform: `translateY(${window.innerHeight + 10}px) rotate(${Math.random() * 360}deg)`,
          },
        ],
        {
          duration: Math.random() * 2000 + 2000, // Random duration between 2-4 seconds
          easing: 'linear',
          fill: 'forwards',
        },
      );

      document.body.appendChild(confetti);

      // Cleanup: Remove confetti after animation
      setTimeout(() => {
        confetti.remove();
      }, 4000);
    }
  };

  const isClaimGreenBonusCalled = () => {
    const { betAmountToClaimBonus } = useGreenBonusStore.getState();
    setTotalBetAmountTill({
      totalBetAmountTill: betAmountToClaimBonus,
    });

    // Trigger confetti
    setShowConfetti(true);
    createConfetti();
    // Stop confetti after 4 seconds
    setTimeout(() => {
      setShowConfetti(false);
    }, 5000);
  };

  useEffect(() => {
    if (isAuthenticated) {
      walletSocket.auth = { token: accessToken };
      walletSocket.connect();

      const handleClaimGreenBonus = () => isClaimGreenBonusCalled();

      walletSocket.on('CLAIM_GREEN_BONUS', handleClaimGreenBonus);

      return () => {
        walletSocket.off('CLAIM_GREEN_BONUS');
        walletSocket.disconnect();
      };
    }
  }, [isAuthenticated, accessToken]);

  const progress = betAmountToClaimBonus
    ? (totalBetAmountTill / betAmountToClaimBonus) * 100
    : 0;

  useEffect(() => {
    if (gameDetails?.isGreenBonusApplicable) {
      setGreenBonusData(gameDetails);
    }
  }, [gameDetails, setGreenBonusData]);
  useEffect(() => {
    const calculatedProgress = gameDetails?.alreadyClaimedOnce
      ? Math.min((progress / 100) * 80 + 20, 100)
      : Math.min(progress, 100);

    // Trigger animation after first render
    const timeout = setTimeout(() => {
      setAnimatedWidth(`${calculatedProgress}%`);
    }, 50); // small delay to ensure DOM is ready

    return () => clearTimeout(timeout);
  }, [progress, gameDetails?.alreadyClaimedOnce]);

  return isLoading ? (
    <div className="flex h-96 items-center justify-center">
      <MainLoader className="w-32" />
    </div>
  ) : gameDetails ? (
    <div className="relative w-full">
      {/* Progress Bar */}
      {isGreenBonusApplicable && (
        <div className="mx-auto  w-full max-w-md md:mt-8 xl:mt-4">
          <div className="relative h-8 w-full overflow-hidden rounded-full bg-gray-800 shadow-md">
            <div className="absolute inset-0 flex items-center justify-between px-2">
              {/* <span className="text-white text-sm font-bold">0</span> */}
              {/* <span className="text-white text-sm font-bold">
                {betAmountToClaimBonus % 1 === 0
                  ? betAmountToClaimBonus.toString()
                  : betAmountToClaimBonus.toFixed(2)}
              </span> */}
            </div>
            <div
              className="text-white flex h-full items-center justify-center bg-gradient-to-r from-green-400 to-blue-500 text-xs font-bold transition-all duration-[2000ms]"
              // style={{ width:gameDetails?.alreadyClaimedOnce? `${Math.min(((progress/100)*80)+20,100)}%`     :` ${Math.min(progress, 100)}%` }}
              style={{ width: animatedWidth }}
            >
              <div
                className="bg-white absolute h-4 w-0.5 transition-all duration-[2000ms]"
                style={{
                  // left: gameDetails?.alreadyClaimedOnce? `${Math.min(((progress/100)*80)+20,100)}%`     :` ${Math.min(progress, 100)}%` ,
                  transform: 'translateX(-50%)',
                  left: animatedWidth,
                }}
              />
              {showConfetti ? (
                <span className="absolute left-1/2 -translate-x-1/2 transform text-sm font-bold text-[#EFD16F] ">
                  {/* {progress.toFixed(2)}% */}
                  Bonus Added To Rewards!
                </span>
              ) : (
                <span className="absolute left-1/2 -translate-x-1/2 transform text-sm font-bold tracking-[10px] text-[#EFD16F]">
                  {/* {progress.toFixed(2)}% */}B O N U S
                </span>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Game Iframe */}
      <iframe
        title="game"
        className="mt-4 aspect-[1031/800] w-full"
        src={gameDetails?.gameUrl}
        frameBorder="0"
      />
    </div>
  ) : (
    <div className="flex h-96 items-center justify-center">
      Something went wrong
    </div>
  );
}

export default GamePage;
