import axios from 'axios';

export async function POST(request) {
  try {
    const { code } = await request.json();

    if (!code) {
      return Response.json(
        { message: 'Authorization code is missing' },
        { status: 400 },
      );
    }

    const { DISCORD_CLIENT_ID, DISCORD_CLIENT_SECRET, DISCORD_REDIRECT_URI } =
      process.env;

    if (!DISCORD_CLIENT_ID || !DISCORD_CLIENT_SECRET || !DISCORD_REDIRECT_URI) {
      console.error('Missing required environment variables for Discord OAuth');
      return Response.json(
        { message: 'Server configuration error' },
        { status: 500 },
      );
    }

    const params = new URLSearchParams({
      client_id: DISCORD_CLIENT_ID,
      client_secret: DISCORD_CLIENT_SECRET,
      grant_type: 'authorization_code',
      code,
      redirect_uri: DISCORD_REDIRECT_URI,
    });

    const { data } = await axios.post(
      'https://discord.com/api/oauth2/token',
      params,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        timeout: 5000, 
      },
    );

    return Response.json(data);
  } catch (error) {
    if (error.response) {
      console.error('Discord OAuth Error:', {
        status: error.response.status,
        data: error.response.data,
      });
    } else if (error.request) {
      console.error('Discord OAuth Error: No response received', error.request);
    } else {
      console.error('Discord OAuth Error:', error.message);
    }
    const status = error.response?.status || 500;
    const message =
      status === 401
        ? 'Authentication failed'
        : status === 429
          ? 'Rate limit exceeded'
          : 'OAuth request failed';

    return Response.json({ message }, { status });
  }
}
