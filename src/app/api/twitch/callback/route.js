import { NextResponse } from 'next/server';

export async function GET(req) {
  try {
    // Extract code from URL parameters
    const url = new URL(req.url);
    const code = url.searchParams.get('code');
    
    // Log the code with more context for debugging
    console.log('Authorization code received:', code ? 'Valid code' : 'Missing code');
    
    // Check if code exists
    if (!code) {
      return NextResponse.json(
        { error: 'Authorization code not found' },
        { status: 400 }
      );
    }
    
    // Get base URL from environment with fallback
    const baseUrl = process.env.NEXT_PUBLIC_AUTH_URL || 'https://dev.fansbets.com/';
    
    // Validate base URL
    if (!baseUrl) {
      console.error('Missing NEXTAUTH_URL environment variable');
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }
    
    // Create redirect URL with properly encoded parameters
    const redirectUrl = new URL('/twitch-signup', baseUrl);
    redirectUrl.searchParams.append('code', code);
    
    // Add state parameter if present in the original request
    const state = url.searchParams.get('state');
    if (state) {
      redirectUrl.searchParams.append('state', state);
    }
    
    // Perform redirect
    return NextResponse.redirect(redirectUrl.toString());
  } catch (error) {
    console.error('Redirect handler error:', error);
    return NextResponse.json(
      { error: 'Failed to process authorization' },
      { status: 500 }
    );
  }
}