import { NextResponse } from 'next/server';
import axios from 'axios';

export async function POST(req) {
  try {
    const { code } = await req.json();

    if (!code) {
      return NextResponse.json(
        { error: 'Authorization code is missing' },
        { status: 400 },
      );
    }

    const clientId = process.env.NEXT_PUBLIC_TWITCH_CLIENT_ID;
    const clientSecret = process.env.TWITCH_CLIENT_SECRET;
    const redirectUri = process.env.TWITCH_REDIRECT_URI;

    if (!clientId || !clientSecret || !redirectUri) {
      console.error('Missing required Twitch OAuth environment variables');
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 },
      );
    }

    const twitchClient = axios.create({
      timeout: 5000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const tokenResponse = await twitchClient.post(
      'https://id.twitch.tv/oauth2/token',
      null,
      {
        params: {
          client_id: clientId,
          client_secret: clientSecret,
          code,
          grant_type: 'authorization_code',
          redirect_uri: redirectUri,
        },
      },
    );

    const { access_token, refresh_token, expires_in } = tokenResponse.data;

    if (!access_token) {
      console.error('No access token returned from Twitch');
      return NextResponse.json(
        { error: 'Authentication failed' },
        { status: 401 },
      );
    }

    const userResponse = await twitchClient.get(
      'https://api.twitch.tv/helix/users',
      {
        headers: {
          Authorization: `Bearer ${access_token}`,
          'Client-Id': clientId,
        },
      },
    );

    const userData = userResponse.data.data[0];

    if (!userData) {
      console.error('No user data returned from Twitch API');
      return NextResponse.json(
        { error: 'Failed to retrieve user data' },
        { status: 404 },
      );
    }

    return NextResponse.json({
      user: userData,
      auth: {
        access_token,
        refresh_token,
        expires_in,
      },
    });
  } catch (error) {
    if (error.response) {
      console.error('Twitch API Error:', {
        endpoint: error.config?.url,
        status: error.response.status,
        data: error.response.data,
      });

      if (error.response.status === 401) {
        return NextResponse.json(
          { error: 'Authentication failed' },
          { status: 401 },
        );
      }
      if (error.response.status === 429) {
        return NextResponse.json(
          { error: 'Rate limit exceeded' },
          { status: 429 },
        );
      }
    } else if (error.code === 'ECONNABORTED') {
      console.error('Twitch API timeout');
      return NextResponse.json(
        { error: 'Connection to Twitch API timed out' },
        { status: 504 },
      );
    } else {
      console.error(
        'Unexpected error during Twitch authentication:',
        error.message,
      );
    }

    return NextResponse.json(
      { error: 'Failed to authenticate with Twitch' },
      { status: 500 },
    );
  }
}
