@import url(./fonts.css);
@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');
@import 'react-datepicker/dist/react-datepicker.css';
@import 'react-loading-skeleton/dist/skeleton.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* ============ Width variable ============ */
    --sidebar-wdith: 15rem;
    --header-heigth: 4.25rem;
    --container-width: 80rem;
    --chat-width: 20.5rem;

    /* ============ Color variable ============ */
    /* ---- Single variable start---- */
    --primary-border: hsla(60, 5%, 19%, 1);
    /* #32322D */
    --sidebar-bg-color: hsla(40, 2%, 10%, 1);
    /* #1B1A1A */
    --input-bg-color: hsla(60, 5%, 19%, 1);
    /* #32322D */
    --secondary-btn-bg-color: hsla(60, 5%, 19%, 1);
    /* #32322D */
    --placeholder-color: hsla(0, 0%, 100%, 0.4);
    /* #ffffff66 */
    --hero-border-color: hsla(34, 49%, 20%, 1); /* #4B361A */
    /* ---- Single variable end---- */


    --primary-500: rgb(229, 34, 39, 0.5);
    /* #E52227 */
    --primary-700: rgb(229, 34, 39, 0.7);
    --primary-800: rgb(229, 34, 39, 0.8);
    --primary-900: rgba(229, 34, 39, 1);
    --primary-1000: rgb(229, 34, 39, 1);

    --cetaceanBlue-1000: rgb(0, 0, 0);
    --cetaceanBlue-2000: #1B1A1A;

    --maastrichtBlue-200: rgb(24, 24, 24, 0.2);
    --maastrichtBlue-500: rgb(24, 24, 24, 0.5);
    --maastrichtBlue-1000: rgb(24, 24, 24);

    --oxfordBlue-900: rgb(10, 37, 77, 0.9);
    --oxfordBlue-1000: rgb(0, 0, 0);

    --steelTeal-100: rgb(206, 202, 180, 0.1);
    --steelTeal-200: hsla(225, 3%, 77%, 1);
    /* #C3C4C7; */
    --steelTeal-500: rgba(206, 202, 180, 1);
    /* #CECAB4 */
    --steelTeal-1000: rgb(206, 202, 180);

    --richBlack-200: rgb(10, 11, 16, 1);
    /* #0A0B10 */
    --richBlack-500: rgb(54, 54, 54, 0.5);
    /* #5C5C5C*/
    --richBlack-700: #5C5C5C;
    --richBlack-600: rgba(54, 54, 54, 0.5);

    /* #363636 */
    --richBlack-900: rgb(54, 54, 54, 0.9);
    --richBlack-1000: rgb(54, 54, 54);

    --fluorescentBlue-1000: rgb(44, 255, 217);

    --green-500: rgb(6, 171, 6, 0.5);
    --green-900: rgb(6, 171, 6, 0.9);
    --green-1000: rgb(6, 171, 6);

    --orange-800: rgb(198, 94, 17, 0.8);
    --orange-1000: rgb(198, 94, 17);

    --black-200: rgb(0, 0, 0, 0.2);
    --black-500: rgb(0, 0, 0, 0.5);
    --black-850: rgb(0, 0, 0, 0.85);
    --black-900: rgb(0, 0, 0, 0.9);
    --black-1000: rgb(0, 0, 0);

    --white-100: rgb(255, 255, 255, 0.1);
    --white-200: hsla(0, 1%, 21%, 1);
    /* #363535 */
    --white-400: hsla(0, 0%, 100%, 0.4);
    /* #ffffff66 */
    --white-500: rgb(255, 255, 255, 0.5);
    --white-700: rgb(255, 255, 255, 0.7);
    --white-750: rgb(255, 255, 255, 0.75);
    --white-1000: rgb(255, 255, 255);

    --tiber-1000: rgba(9, 42, 60, 1);

    --scarlet-700: rgba(255, 76, 62, 1);
    --scarlet-900: rgba(182, 14, 1, 1);

    --slate-gray-400: rgba(127, 133, 164, 1);
    --slate-gray-500: rgba(63, 85, 94, 1);
    --slate-gray-800: rgba(41, 41, 41, 1);
    /* #292929*/

    --dark-blue-800: #021634;

    --red-1000: rgba(182, 14, 1, 1);

    --golden-500: rgb(211, 155, 70, 0.5);
    /* #D39B46 */

    --border-color-100: rgba(187, 119, 51, 1);
    /* #BB7733 */
    --color-1000: #32322D;
    --color-2000: #767676;
  }
}

@layer components {
  /* Form code select filed */

  .form-input-select > .form-input__control {
    @apply cursor-pointer rounded-md border border-solid border-transparent bg-maastrichtBlue-1000 leading-none focus:border focus:border-solid focus:border-borderColor-100;
  }

  .form-input-select > .form-input__control:hover {
    @apply border border-solid border-transparent;
  }

  .form-input-select > .form-input__control.form-input__control--is-focused {
    @apply border border-solid border-borderColor-100 shadow-none outline-none;
  }

  .form-input-select > .form-input__control > .form-input__value-container {
    @apply px-4 py-3;
  }

  .form-input-select > .form-input__control > .form-input__value-container > .form-input__input-container > .form-input__input {
    @apply !text-steelTeal-1000;
  }

  .form-input-select > .form-input__control > .form-input__value-container > .form-input__single-value {
    @apply text-white-1000;
  }

  .form-input-select > .form-input__control > .form-input__indicators > .form-input__indicator-separator {
    @apply hidden;
  }

  .form-input-select > .form-input__control > .form-input__value-container > .form-input__placeholder {
    @apply text-steelTeal-1000;
  }

  .form-input-select > .form-input__menu {
    @apply rounded-md bg-tiber-1000;
  }

  .form-input-select > .form-input__menu > .form-input__menu-list {
    @apply p-2;
  }

  .form-input-select > .form-input__menu > .form-input__menu-list > .form-input__option {
    @apply text-white-1000 cursor-pointer bg-transparent;
  }

  .form-input-select > .form-input__menu > .form-input__menu-list > .form-input__option--is-focused {
    @apply rounded-md bg-transparent bg-select-hover;
  }

  .form-input-select > .form-input__control > .form-input__indicators > .form-input__indicator > svg {
    @apply text-steelTeal-1000;
  }

  /* Mobile code select filed */

  .mobile-input-select > .mobile-input__control {
    @apply cursor-pointer rounded-md border border-solid border-transparent bg-maastrichtBlue-1000 leading-none focus:border focus:border-solid focus:border-borderColor-100;
  }

  .mobile-input-select > .mobile-input__control:hover {
    @apply border border-solid border-transparent;
  }

  .mobile-input-select > .mobile-input__control.mobile-input__control--is-focused {
    @apply border border-solid border-borderColor-100 shadow-none outline-none;
  }

  .mobile-input-select > .mobile-input__control > .mobile-input__value-container {
    @apply py-3 pl-1.5 pr-0.5;
  }

  .mobile-input-select > .mobile-input__control > .mobile-input__value-container > .mobile-input__input-container > .mobile-input__input {
    @apply !text-steelTeal-1000;
  }

  .mobile-input-select > .mobile-input__control > .mobile-input__value-container > .mobile-input__single-value {
    @apply text-white-1000;
  }

  .mobile-input-select > .mobile-input__control > .mobile-input__indicators > .mobile-input__indicator {
    @apply pl-0.5 pr-1.5;
  }

  .mobile-input-select > .mobile-input__control > .mobile-input__indicators > .mobile-input__indicator-separator {
    @apply hidden;
  }

  .mobile-input-select > .mobile-input__control > .mobile-input__value-container > .mobile-input__placeholder {
    @apply text-steelTeal-1000;
  }

  .mobile-input-select > .mobile-input__menu {
    @apply rounded-md bg-tiber-1000;
  }

  .mobile-input-select > .mobile-input__menu > .mobile-input__menu-list {
    @apply p-2;
  }

  .mobile-input-select > .mobile-input__menu > .mobile-input__menu-list > .mobile-input__option {
    @apply text-white-1000 cursor-pointer;
  }

  .mobile-input-select > .mobile-input__menu > .mobile-input__menu-list > .mobile-input__option.mobile-input__option--is-selected {
    @apply bg-transparent;
  }

  .mobile-input-select > .mobile-input__menu > .mobile-input__menu-list > .mobile-input__option--is-focused {
    @apply rounded-md bg-transparent bg-select-hover;
  }

  .mobile-input-select > .mobile-input__control > .mobile-input__indicators > .mobile-input__indicator > svg {
    @apply text-steelTeal-1000;
  }

  /* For coin switch */
  .switch[data-ison='true'] {
    @apply flex-row-reverse;
  }

  /* Form code select filed */

  .profile-menu-select > .profile-menu__control {
    @apply cursor-pointer rounded-md border border-solid border-transparent bg-maastrichtBlue-1000 bg-select-hover leading-none focus:border focus:border-solid focus:border-borderColor-100;
  }

  .profile-menu-select > .profile-menu__control:hover {
    @apply border border-solid border-transparent;
  }

  .profile-menu-select > .profile-menu__control.profile-menu__control--is-focused {
    @apply border border-solid border-steelTeal-1000 shadow-none outline-none;
  }

  .profile-menu-select > .profile-menu__control > .profile-menu__value-container {
    @apply px-4 py-3;
  }

  .profile-menu-select > .profile-menu__control > .profile-menu__value-container > .profile-menu__input-container > .profile-menu__input {
    @apply !text-white-1000;
  }

  .profile-menu-select > .profile-menu__control > .profile-menu__value-container > .profile-menu__single-value {
    @apply text-xl text-white-1000 max-sm:text-base;
  }

  .profile-menu-select > .profile-menu__control > .profile-menu__indicators > .profile-menu__indicator-separator {
    @apply hidden;
  }

  .profile-menu-select > .profile-menu__control > .profile-menu__value-container > .profile-menu__placeholder {
    @apply text-steelTeal-1000;
  }

  .profile-menu-select > .profile-menu__menu {
    @apply rounded-md bg-tiber-1000;
  }

  .profile-menu-select > .profile-menu__menu > .profile-menu__menu-list {
    @apply p-2;
  }

  .profile-menu-select > .profile-menu__menu > .profile-menu__menu-list > .profile-menu__option {
    @apply text-white-1000 cursor-pointer;
  }

  .profile-menu-select > .profile-menu__menu > .profile-menu__menu-list > .profile-menu__option--is-focused {
    @apply rounded-md bg-transparent bg-select-hover;
  }

  .profile-menu-select > .profile-menu__control > .profile-menu__indicators > .profile-menu__indicator > svg {
    @apply text-white-1000;
  }

  /* Provider select filed */

  .provider-react-select {
    @apply w-full max-w-providerWidth;
  }

  .provider-react-select > .provider-react__control {
    @apply cursor-pointer rounded-[10px] border-none bg-maastrichtBlue-1000 leading-none focus:border-none;
  }

  .provider-react-select > .provider-react__control:hover {
    @apply border-none;
  }

  .provider-react-select > .provider-react__control.provider-react__control--is-focused {
    @apply border-none shadow-none outline-none;
  }

  .provider-react-select > .provider-react__control > .provider-react__value-container {
    @apply px-2 py-1 pr-0;
  }

  .provider-react-select > .provider-react__control > .provider-react__value-container > .provider-react__input-container > .provider-react__input {
    @apply text-sm !text-steelTeal-1000;
  }

  .provider-react-select > .provider-react__control > .provider-react__value-container > .provider-react__single-value {
    @apply text-sm text-steelTeal-1000;
  }

  .provider-react-select > .provider-react__control > .provider-react__indicators > .provider-react__indicator-separator {
    @apply hidden;
  }

  .provider-react-select > .provider-react__control > .provider-react__value-container > .provider-react__placeholder {
    @apply text-sm text-steelTeal-1000;
  }

  .provider-react-select > .provider-react__menu {
    @apply rounded-md bg-tiber-1000;
  }

  .provider-react-select > .provider-react__menu > .provider-react__menu-list {
    @apply p-2;
  }

  .provider-react-select > .provider-react__menu > .provider-react__menu-list > .provider-react__option {
    @apply text-sm text-white-1000 cursor-pointer;
  }

  .provider-react-select > .provider-react__menu > .provider-react__menu-list > .provider-react__option--is-focused {
    @apply rounded-md bg-transparent bg-select-hover;
  }

  .provider-react-select > .provider-react__control > .provider-react__indicators > .provider-react__indicator > svg {
    @apply text-steelTeal-1000;
  }

  /* Sort select filed */

  .sort-react-select {
    @apply w-full max-w-[200px];
  }

  .sort-react-select > .sort-react__control {
    @apply cursor-pointer rounded-[10px] border-none bg-cetaceanBlue-1000 leading-none focus:border-none;
  }

  .sort-react-select > .sort-react__control:hover {
    @apply border-none;
  }

  .sort-react-select > .sort-react__control.sort-react__control--is-focused {
    @apply border-none shadow-none outline-none;
  }

  .sort-react-select > .sort-react__control > .sort-react__value-container {
    @apply px-2 py-2.5 pl-20 before:absolute before:left-7 before:text-sm before:text-slateGray-400 before:content-['Sort_by:_'] after:absolute after:left-2 after:content-[url(../assets/images/svg-images/swap.svg)];
  }

  .sort-react-select > .sort-react__control > .sort-react__value-container > .sort-react__input-container > .sort-react__input {
    @apply text-sm !text-steelTeal-1000;
  }

  .sort-react-select > .sort-react__control > .sort-react__value-container > .sort-react__single-value {
    @apply text-sm text-white-1000;
  }

  .sort-react-select > .sort-react__control > .sort-react__indicators > .sort-react__indicator-separator {
    @apply hidden;
  }

  .sort-react-select > .sort-react__control > .sort-react__value-container > .sort-react__placeholder {
    @apply text-sm text-white-1000;
  }

  .sort-react-select > .sort-react__menu {
    @apply rounded-md bg-tiber-1000;
  }

  .sort-react-select > .sort-react__menu > .sort-react__menu-list {
    @apply p-2;
  }

  .sort-react-select > .sort-react__menu > .sort-react__menu-list > .sort-react__option {
    @apply text-sm text-white-1000 cursor-pointer;
  }

  .sort-react-select > .sort-react__menu > .sort-react__menu-list > .sort-react__option--is-focused {
    @apply rounded-md bg-transparent bg-select-hover;
  }

  .sort-react-select > .sort-react__control > .sort-react__indicators > .sort-react__indicator > svg {
    @apply text-white-1000;
  }

  /* Form code select filed */

  .currency-select > .currency-select-inner__control {
    @apply cursor-pointer rounded-md border border-solid border-transparent bg-transparent leading-none focus:border focus:border-solid focus:border-borderColor-100 min-h-8;
  }

  .currency-select > .currency-select-inner__control:hover {
    @apply border border-solid border-transparent;
  }

  .currency-select > .currency-select-inner__control.currency-select-inner__control--is-focused {
    @apply border-none shadow-none outline-none;
  }

  .currency-select > .currency-select-inner__control > .currency-select-inner__value-container {
    @apply pl-2 pr-0 py-0.5;
  }

  .currency-select > .currency-select-inner__control > .currency-select-inner__value-container > .currency-select-inner__input-container > .currency-select-inner__input {
    @apply !text-white-1000;
  }

  .currency-select > .currency-select-inner__control > .currency-select-inner__value-container > .currency-select-inner__single-value {
    @apply text-sm text-white-1000 max-sm:text-base;
  }

  .currency-select > .currency-select-inner__control > .currency-select-inner__indicators > .currency-select-inner__indicator-separator {
    @apply hidden;
  }

  .currency-select > .currency-select-inner__control > .currency-select-inner__indicators > .currency-select-inner__indicator {
    @apply p-0 pr-2;
  }

  .currency-select > .currency-select-inner__control > .currency-select-inner__value-container > .currency-select-inner__placeholder {
    @apply text-sm text-white-1000;
  }

  .currency-select > .currency-select-inner__menu {
    @apply rounded-md bg-tiber-1000;
  }

  .currency-select > .currency-select-inner__menu > .currency-select-inner__menu-list {
    @apply p-2;
  }

  .currency-select > .currency-select-inner__menu > .currency-select-inner__menu-list > .currency-select-inner__option {
    @apply text-white-1000 text-center px-2 text-sm cursor-pointer;
  }

  .currency-select > .currency-select-inner__menu > .currency-select-inner__menu-list > .currency-select-inner__option--is-focused {
    @apply rounded-md bg-transparent bg-walletBg;
  }

  .currency-select > .currency-select-inner__menu > .currency-select-inner__menu-list > .currency-select-inner__option--is-selected {
    @apply rounded-md bg-transparent bg-walletBg;
  }

  .currency-select > .currency-select-inner__control > .currency-select-inner__indicators > .currency-select-inner__indicator > svg {
    @apply text-white-1000;
  }

  /* Datepicker */
  .react-datepicker-wrapper {
    @apply max-w-[250px] w-full h-[50px];
  }

  /* ---- Chat Multi Select Start ---- */

  .chatMulti-select > .chatMulti-inner-select__control {
    @apply bg-transparent border-none;
  }

  .chatMulti-select > .chatMulti-inner-select__control.chatMulti-inner-select__control--is-focused {
    @apply border-none shadow-none outline-none;
  }

  .chatMulti-select > .chatMulti-inner-select__control > .chatMulti-inner-select__group > .chatMulti-inner-select__multiOption > .chatMulti-inner-select__placeholder {
    @apply hidden;
  }


  .chatMulti-select > .chatMulti-inner-select__control > .chatMulti-inner-select__group > .chatMulti-inner-select__multiOption > .chatMulti-inner-select__multi-value {
    @apply bg-transparent border border-solid rounded flex items-center;
  }

  .chatMulti-select > .chatMulti-inner-select__control > .chatMulti-inner-select__group > .chatMulti-inner-select__multiOption > .chatMulti-inner-select__multi-value > .chatMulti-inner-select__multi-value__remove {
    @apply w-3 h-3 border border-solid border-white-1000 rounded-full p-[1px] mx-1 hover:bg-transparent hover:text-gray-400 hover:border-gray-400;
  }

  .chatMulti-select > .chatMulti-inner-select__control > .chatMulti-inner-select__group > .chatMulti-inner-select__input-container-section > .chatMulti-inner-select__input-container {
    @apply h-full m-0 text-white-1000;
  }

  .chatMulti-select > .chatMulti-inner-select__menu {
    @apply bg-black-1000 border border-solid border-richBlack-1000;
  }

  .chatMulti-select > .chatMulti-inner-select__menu > .chatMulti-inner-select__menu-list > .chatMulti-inner-select__option {
    @apply cursor-pointer;
  }

  .chatMulti-select > .chatMulti-inner-select__menu > .chatMulti-inner-select__menu-list {
    @apply py-0;
  }

  .chatMulti-select > .chatMulti-inner-select__menu > .chatMulti-inner-select__menu-list > .chatMulti-inner-select__option.chatMulti-inner-select__option--is-focused {
    @apply bg-richBlack-1000;
  }

  /* .chatMulti-select > .chatMulti-inner-select__control > .chatMulti-inner-select__group > .chatMulti-inner-select__multiOption > .chatMulti-inner-select__multi-value {
    @apply pt-4;
  } */

  /* .chatMulti-select > .chatMulti-inner-select__control > .chatMulti-inner-select__value-container {
    @apply border-none pt-14 pb-4 px-4 rounded-md bg-richBlack-500 flex;
  }

  .chatMulti-select > .chatMulti-inner-select__control > .chatMulti-inner-select__value-container > .chatMulti-inner-select__placeholder {
    @apply hidden;
  } */


  /* .chatMulti-select > .chatMulti-inner-select__control > .chatMulti-inner-select__value-container > .chatMulti-inner-select__input-container{
    @apply order-[-1] basis-full w-full absolute top-0 left-0 bg-black-1000 px-9 mx-0 mt-0  border border-solid border-borderColor-100 h-[42px] rounded-md before:content-[''] before:absolute before:top-1/2 before:-translate-y-1/2 before:left-3 before:bg-chatSearchIcon before:rounded-md before:z-[1] before:bg-no-repeat before:bg-center before:w-5 before:h-5 mb-0;
  } */
  /* before:content-[''] before:absolute before:inset-0 before:bg-searchBorder before:rounded-md before:z-[1]; */

  /* .chatMulti-select > .chatMulti-inner-select__control > .chatMulti-inner-select__value-container > .chatMulti-inner-select__input-container > .chatMulti-inner-select__input{
    @apply relative z-[2] bg-black-1000 border-2 border-solid border-transparent;
  } */


  .chatMulti-select > .chatMulti-inner-select__control > .chatMulti-inner-select__indicators {
    @apply absolute top-0.5 right-0;
  }

  .chatMulti-select > .chatMulti-inner-select__control > .chatMulti-inner-select__indicators > .chatMulti-inner-select__indicator-separator {
    @apply hidden;
  }

  .chatMulti-select > .chatMulti-inner-select__control > .chatMulti-inner-select__indicators > .chatMulti-inner-select__dropdown-indicator {
    @apply hidden;
  }


  /* ---- Chat Multi Select End ---- */
}


* {
  scrollbar-width: thin;
  scrollbar-color: var(--maastrichtBlue-1000) var(--richBlack-1000);
  user-select: none;
}

body {
  font-family: "Inter", sans-serif !important;
  color: var(--white-1000);
}

:focus-visible {
  outline: none !important;
}

.swiper-wrapper {
  .swiper-slide {
    transform-origin: bottom !important;

    &.swiper-slide-thumb-active {
      transform: scale(1.1) translateZ(0) !important;
    }
  }
}

.coin-shadow {
  filter: drop-shadow(0px 1px 2px var(--black-500));
}

.scrollbar-none {
  scrollbar-width: none;
}

.header-blur {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.bottom-menu-blur {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.section-blur {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  box-shadow: inset 0 0 3.125rem 3.125rem var(--richBlack-1000);
  -webkit-background-clip: text;
  -webkit-text-fill-color: var(--white-1000);
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

em-emoji-picker > section {
  width: 300px !important;
}

.hide-arrows::-webkit-outer-spin-button,
.hide-arrows::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.hide-arrows {
  -moz-appearance: textfield;
  appearance: textfield;
}

input[type='number'] {
  cursor: text;
}

input:-webkit-autofill {
  caret-color: #ffffff !important;
}

input:autofill {
  caret-color: #ffffff !important;
}


.gradient-text {
  color: transparent;
  background: linear-gradient(0deg, #BB7733 0%, #C17F37 7%, #D19844 20.01%, #EABB58 33.01%, #EECE6C 47.02%, #F2DD7B 61.02%, #ECC764 82.03%, #EABB58 99.03%);
  background-clip: text;
  -webkit-background-clip: text;
}

progress::-webkit-progress-value {
  background: linear-gradient(0deg, #BB7733 0%, #C17F37 7%, #D19844 20.01%, #EABB58 33.01%, #EECE6C 47.02%, #F2DD7B 61.02%, #ECC764 82.03%, #EABB58 99.03%);
  ;
  border-radius: 1rem;
}

/* Customize the progress track (the empty part) */
progress::-webkit-progress-bar {
  background-color: #5C5C5C;
  border-radius: 1rem;
}


/* .blurColor::before {
  position: fixed;
  content: '';
  width: 15%;
  height: 100dvh;
  background: #e5222796;
  -webkit-filter: blur(97px);
  filter: blur(97px);
  z-index: 1;
  left: 0;
  top: 0;
  margin: 0 auto;
  border-radius: 0 100% 100% 0;
  z-index: -1;

} */

/* .blurColor::after {
  position: fixed;
  content: '';
  width: 15%;
  height: 100dvh;
  background: #e5222796;
  -webkit-filter: blur(97px);
  filter: blur(97px);
  z-index: 1;
  right: 5%;
  top: 0;
  margin: 0 auto;
  border-radius: 100% 0% 0% 100%;
  z-index: -1;

} */
/* .blurColorChatWindow::after {
  position: fixed;
  content: '';
  width: 15%;
  height: 100dvh;
  background: #e5222796;
  -webkit-filter: blur(97px);
  filter: blur(97px);
  z-index: 1;
  top: 0;
  margin: 0 auto;
  border-radius: 100% 0% 0% 100%;
  z-index: -1;

} */