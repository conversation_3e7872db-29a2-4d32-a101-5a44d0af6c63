'use client';

import React, { Fragment, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import GameCard from '@/components/Common/GameCard';
import MainLoader from '@/components/Common/Loader/MainLoader';
import useCategory from '@/hooks/useCategory';

import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import useAuthStore from '@/store/useAuthStore';
import useAuthTab from '@/store/useAuthTab';
import useModalStore from '@/store/useModalStore';
import Auth from '@/components/Auth';
import toast from 'react-hot-toast';

function GamePage({ params }) {
  const gameName = decodeURIComponent(params?.categoryName);
  const [pageNo, setPageNo] = useState(1);
  const {
    casinoGames,
    refetch,
    gamesLoading,
    isFetching,
    isFetchingNextPage,
    hasNextPage,
    fetchNextPage,
  } = useCategory({ limit: 14, categoryName: gameName, pageNo });
  const { isAuthenticated } = useAuthStore((state) => state);
  const { setSelectedTab } = useAuthTab((state) => state);
  const { openModal } = useModalStore((state) => state);

  const router = useRouter();

  const handleGameClick = (gameId) => {
    if (!isAuthenticated) {
      localStorage.setItem('activeTab', 0);
      setSelectedTab(0);
      openModal(<Auth />);
      return;
    }
    router.push(`/game/${gameId}`);
  };

  return gamesLoading ? (
    <MainLoader className="w-32" />
  ) : casinoGames?.pages?.length > 0 ? (
    <section className="mb-10 rounded-lg bg-oxfordBlue-1000 p-3 shadow-container">
      <div className="mb-4 flex items-center justify-between gap-4">
        <h6 className="text-xl font-bold text-white-1000">{gameName}</h6>
      </div>
      <div className="grid grid-cols-2 gap-4 xs:grid-cols-3 md:grid-cols-5">
        {casinoGames?.pages?.map((group, index) => {
          return (
            <Fragment key={index}>
              {group?.data?.casinoGames?.rows?.map((game) => (
                <GameCard
                  key={game?.id}
                  src={game?.iconUrl}
                  alt={game?.name}
                  gameId={game?.id}
                  isFavorite={game?.isFavorite}
                  width="10000"
                  height="10000"
                  onClick={() => handleGameClick(game?.id)}
                />
              ))}
            </Fragment>
          );
        })}
      </div>
      {hasNextPage && (
        <div className="mt-8 flex items-center justify-center">
          <PrimaryButton
            onClick={() => fetchNextPage()}
            disabled={!hasNextPage || isFetchingNextPage}
          >
            {isFetchingNextPage
              ? 'Loading...'
              : hasNextPage
                ? 'Load More'
                : 'Nothing more to load'}
          </PrimaryButton>
        </div>
      )}
    </section>
  ) : (
    <div className="flex h-96 items-center justify-center">
      No games are available
    </div>
  );
}

export default GamePage;
