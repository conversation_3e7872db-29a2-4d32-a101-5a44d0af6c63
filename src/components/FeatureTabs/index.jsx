'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import CasinoTabIcon from '@/assets/icons/CasinoTabIcon';
import SportTabIcon from '@/assets/icons/SportTabIcon';

export default function CustomTabs() {
    const [activeTab, setActiveTab] = useState('casino');
    const tabs = ['casino', 'sport'];

    return (
        <div className="max-w-full">
            {/* Tabs Container */}
            <div className="relative h-10 inline-flex rounded-px_10 overflow-hidden bg-sidebarBgColor border-[1.5px] border-solid border-primaryBorder">
                {/* Tab Buttons */}
                {tabs.map((tab) => (
                    <button
                        key={tab}
                        onClick={() => setActiveTab(tab)}
                        className={`relative z-10 px-4 py-2 text-sm font-semibold transition-colors duration-300 ${activeTab === tab ? 'text-black-1000' : 'text-white'}`}
                    >
                        {activeTab === tab && (
                            <motion.div
                                layoutId="activeBackground"
                                className={`absolute inset-0 bg-TintGoldGradient z-[-1]`}
                                transition={{ stiffness: 500, damping: 30 }}
                            />
                        )}
                        {tab.charAt(0).toUpperCase() + tab.slice(1)}

                        {tab === 'casino' && (
                            <CasinoTabIcon className='absolute bottom-0 left-0' />
                        )}
                        {tab === 'sport' && (
                            <SportTabIcon className='absolute bottom-0 left-0' />
                        )}
                    </button>
                ))}
            </div>

            {/* Animated Tab Content */}
            {/* <div className="mt-6 bg-white rounded-lg shadow p-4 min-h-[120px]">
        <AnimatePresence mode="wait">
          {activeTab === 'casino' && (
            <motion.div
              key="casino"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.3 }}
            >
              <h2 className="text-xl font-bold mb-2">Casino Games</h2>
              <p>Explore our wide range of casino games including slots, roulette, and more.</p>
            </motion.div>
          )}

          {activeTab === 'sport' && (
            <motion.div
              key="sport"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.3 }}
            >
              <h2 className="text-xl font-bold mb-2">Sports Betting</h2>
              <p>Bet on your favorite sports including cricket, football, and basketball.</p>
            </motion.div>
          )}
        </AnimatePresence>
      </div> */}
        </div>
    );
}
