'use client';

import React, { useEffect, useMemo } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import useGeneralStore from '@/store/useGeneralStore';
import useAuthStore from '@/store/useAuthStore';
import HomeIcon from '@/assets/icons/HomeIcon';
import FavoriteIcon from '@/assets/icons/Favorite';
import ChatIcon from '@/assets/icons/Chat';
import ProfileIcon from '@/assets/icons/Profile';

function BottomMenu() {
  const { setOpenChat, setActiveMenu, activeMenu } = useGeneralStore();
  const { isAuthenticated } = useAuthStore();
  const router = useRouter();
  const pathname = usePathname();

  const menuItems = useMemo(
    () => [
      {
        title: '/',
        icon: <HomeIcon className="h-5 w-5 fill-steelTeal-1000" />,
        onClick: () => {
          setActiveMenu('/');
          setOpenChat(false);
          router.push('/');
        },
      },
      {
        title: '/favorites',
        icon: <FavoriteIcon className="h-5 w-5 fill-steelTeal-1000" />,
        onClick: () => {
          setActiveMenu('/favorites');
          setOpenChat(false);
          router.push('/favorites');
        },
      },
      {
        title: 'msg',
        icon: <ChatIcon className="h-5 w-5 fill-steelTeal-1000" />,
        onClick: () => {
          setActiveMenu('msg');
          setOpenChat(true);
        },
      },
      {
        title: '/user',
        icon: <ProfileIcon className="h-5 w-5 fill-steelTeal-1000" />,
        onClick: () => {
          setActiveMenu('/user');
          setOpenChat(false);
          router.push('/user');
        },
      },
    ],
    [setActiveMenu, setOpenChat, router],
  );

  useEffect(() => {
    setActiveMenu(pathname);
  }, [pathname, setActiveMenu]);

  if (!isAuthenticated) return null;

  return (
    <nav className="bottom-menu-blur fixed bottom-0 left-0 right-0 z-50 bg-maastrichtBlue-500 shadow-bottom-menu xl:hidden">
      <div className="mx-auto flex h-[3.75rem] max-w-[26.563rem] items-center justify-between gap-[0.75rem] px-[2.375rem]">
        {menuItems.map((item) => (
          <button
            key={item.title}
            type="button"
            className={`group flex h-10 w-10 items-center justify-center rounded-md ${
              activeMenu === item.title
                ? 'bg-primary-1000'
                : 'bg-richBlack-1000'
            }`}
            onClick={item.onClick}
          >
            {item.icon}
          </button>
        ))}
      </div>
    </nav>
  );
}

export default React.memo(BottomMenu);
