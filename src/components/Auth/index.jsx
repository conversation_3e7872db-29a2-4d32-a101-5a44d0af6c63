'use client';

import React, { useEffect } from 'react';
import Image from 'next/image';
import { X } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

import useAuthStore from '@/store/useAuthStore';
import useHelperHook from '@/hooks/useHelperHook';
import useModalStore from '@/store/useModalStore';
import useAuthTab from '@/store/useAuthTab';

import { useRouter } from 'next/navigation';

import AuthCharacterSignup from '../../assets/images/stock-images/auto-img.png';
import AuthCharacterSignin from '../../assets/images/stock-images/auth-character-login.png';
import SignUp from './SignUp';
import SignIn from './SignIn';
import Tabs from '../Common/Tabs';
import IconButton from '../Common/Button/IconButton';

function Auth() {
  const router = useRouter();
  const { isAuthenticated, hasRehydrated, setUserDetails } = useAuthStore((state) => state);
  const { clearUserAuth } = useHelperHook();
  const { clearModals } = useModalStore((state) => state);
  const { selectedTab, setSelectedTab } = useAuthTab((state) => state);

  const tabs = [
    {
      label: 'Register',
      content: <SignUp />,
    },
    {
      label: 'Login',
      content: <SignIn />,
    },
  ];

  useEffect(() => {
    if (!isAuthenticated) {
      setUserDetails([]);
    }
  }, [isAuthenticated]);

  const handleCloseModal = () => {
    clearModals();
  };

  useEffect(() => {
    window.addEventListener('logout', () => {
      clearUserAuth();
      localStorage.clear();
      clearModals();
      router.push('/');
    });
    setSelectedTab(Number(localStorage.getItem('activeTab')) || 0);
  }, []);

  return (
    <AnimatePresence>
      {!isAuthenticated && hasRehydrated && (
        <div
          id="authentication-modal"
          tabIndex="-1"
          aria-hidden="true"
          className="fixed inset-0 z-50 flex items-end md:items-center justify-center bg-black-850"
        >
          <motion.div
            key="auth-modal"
            initial={{ y: '100%', opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: '100%', opacity: 0 }}
            transition={{ duration: 0.4, ease: 'easeOut' }}
            className="relative max-h-full md:min-h-[31.688rem] w-full max-w-[53.25rem] md:p-4 p-0"
          >
            <div className="relative overflow-hidden rounded-[0.625rem] bg-cetaceanBlue2-2000">
              <div className="absolute right-3 top-3">
                <IconButton onClick={handleCloseModal} className="h-6 w-6 min-w-6">
                  <X className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
                </IconButton>
              </div>
              <div className="flex justify-center">
                <div className="flex w-full max-md:flex-col max-md:items-center">
                  <div className="w-full max-w-[30rem] max-md:hidden">
                    <Image
                      src={selectedTab === 0 ? AuthCharacterSignup : AuthCharacterSignin}
                      width={1000}
                      height={1000}
                      className="w-full h-full max-w-[30rem] object-cover"
                      alt="Auth Character"
                    />
                  </div>

                  <div className="w-full max-w-[30.25rem] max-md:max-w-full p-4 [&>.tabsContent]:mt-4">
                    <Tabs
                      classes="[&>ul>li>button]:font-bold"
                      tabs={tabs}
                      setSelectedTab={setSelectedTab}
                    />
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
}

export default Auth;
