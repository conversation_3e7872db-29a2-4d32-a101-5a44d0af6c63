'use client';

import React, { useState } from 'react';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import InputField from '@/components/Common/InputField';
import { forgotPasswordSchema } from '@/schemas/auth';
import useForgotPassword from '@/hooks/useForgotPassword';

const ForgotPassword = ({ onBack }) => {
  const [email, setEmail] = useState('');
  const [formErrors, setFormErrors] = useState({ email: '' });
  const { forgotPassword, error, isLoading } = useForgotPassword();

  const handleSubmit = (e) => {
    e.preventDefault();
    try {
      forgotPasswordSchema.parse({ email });
      forgotPassword(email);
    } catch (validationError) {
      setFormErrors(validationError.formErrors.fieldErrors);
    }
  };

  const handleChange = (e) => {
    setEmail(e.target.value);
    setFormErrors({ email: '' });
  };

  return (
    <form onSubmit={handleSubmit}>
      <p className="text-base text-steelTeal-1000">
        Please enter your email. We will send you a new password.
      </p>
      <InputField
        type="email"
        name="email"
        value={email}
        placeholder="Email"
        onChange={handleChange}
        error={formErrors.email}
        label={'Email'}
      />
      <div className="mt-6 flex justify-between">
        <PrimaryButton type="button" onClick={onBack}>
          Back
        </PrimaryButton>
        <PrimaryButton type="submit" disabled={isLoading}>
          {isLoading ? 'Sending...' : 'Reset Password'}
        </PrimaryButton>
      </div>
      {error && <p className="mt-4 text-red-500">{error}</p>}
    </form>
  );
};

export default ForgotPassword;
