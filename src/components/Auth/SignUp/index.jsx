import React, { useEffect, useState } from 'react';
import { useGoogleLogin } from '@react-oauth/google';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import useSignUp from '@/hooks/useSignUp';
import InputField from '@/components/Common/InputField';
import { signUpSchema } from '@/schemas/auth';
import ButtonLoader from '@/components/Common/Loader/ButtonLoader';
import FacebookIcon from '@/assets/icons/FacebookIcon';
import DiscordIcon from '@/assets/icons/DiscordIcon';
import TwitchIcon from '@/assets/icons/TwitchIcon';
import GoogleIcon from '@/assets/icons/GoogleIcon';
// import OnlyFans from '@/assets/icons/onlyfansIcon.svg';
import { useFacebookLoginMutation } from '@/reactQuery/authQuery';
import ForcedEmailModal from '@/components/Models/ForcedEmailModal';
import useModalStore from '@/store/useModalStore';
import ReferralCodeBox from '../ReferalInput';

function SignUp() {
  const [formData, setFormData] = useState({
    username: '',
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    // acceptTerms: false,
    // over18: false,
  });


  const { closeModal, openModal } = useModalStore((state) => state);

  const { signUp, isLoading, googleMutation } = useSignUp();




  useEffect(() => {
    // Load the Facebook SDK
    window.fbAsyncInit = function () {
      FB.init({
        appId: process.env.NEXT_PUBLIC_FACEBOOK_APP_ID,
        cookie: true,
        xfbml: true,
        version: 'v11.0',
      });
      FB.AppEvents.logPageView();
    };

    (function (d, s, id) {
      let js;
      const fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) {
        return;
      }
      js = d.createElement(s);
      js.id = id;
      js.src = 'https://connect.facebook.net/en_US/sdk.js';
      fjs.parentNode.insertBefore(js, fjs);
    })(document, 'script', 'facebook-jssdk');
  }, []);

  const [formErrors, setFormErrors] = useState({
    username: '',
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    // acceptTerms: false,
    // over18: false,
  });


  const handleSubmit = (e) => {
    e.preventDefault();

    try {
      signUpSchema.parse({ ...formData });
      signUp({
        ...formData,
        password: btoa(formData.password),
        confirmPassword: btoa(formData.confirmPassword),
      });
    } catch (validationError) {
      setFormErrors(validationError.formErrors.fieldErrors);
    }
  };

  const handleGoogleLogin = useGoogleLogin({
    onSuccess: (tokenResponse) => {
      if (tokenResponse) {
        const userData = {
          credential: tokenResponse.access_token,
          isSignup: true,
          isTermsAccepted: true,
        };
        googleMutation.mutate(userData);
      }
    },
    onError: (errorResponse) =>
      console.log(errorResponse, ':::::google res login'),
  });

  const handleFacebookClick = (e) => {
    e.preventDefault();
    FB.login(
      function (response) {
        if (response && response.authResponse && response.authResponse.userID) {
          FB.api(
            `/${response.authResponse.userID}`,
            { fields: ['first_name', 'last_name', 'email'] },
            function (_response) {
              responseFacebook(_response);
            },
          );
        }
      },
      { scope: 'public_profile,email' },
    );
  };

  const responseFacebook = (response) => {
    const userData = {
      firstName: response.first_name,
      lastName: response.last_name,
      userId: response.id,
      email: response.email,
      isSignup: true,
      // isTermsAccepted: true,
      isForceEmail: false,
    };

    if (response && response.email) {
      handleFaceBookLogin(userData);
    } else {
      openModal(<ForcedEmailModal userData={userData} />);
    }
  };
  const facebookMutation = useFacebookLoginMutation({
    onSuccess: (response) => {
      console.log(response, ':::::::::facebook response');
    },
    onError: (error) => {
      console.log(error, '::::::::::::facebook error');
    },
  });
  const handleFaceBookLogin = async (userData) => {
    facebookMutation.mutate(userData);
  };

  const handleChange = (e) => {
    const { name, value, checked } = e.target;

    if (name === 'over18' || name === 'acceptTerms') {
      setFormData({
        ...formData,
        [name]: checked,
      });
      setFormErrors({
        ...formErrors,
        [name]: false,
      });
    } else {
      setFormData({
        ...formData,
        [name]: value.trim(),
      });
      setFormErrors({
        ...formErrors,
        [name]: '',
      });
    }
  };

  const signInWithDiscord = () => {
    const discordAuthUrl = `https://discord.com/oauth2/authorize?client_id=${process.env.NEXT_PUBLIC_DISCORD_CLIENT_ID}&response_type=code&redirect_uri=${encodeURIComponent(process.env.NEXT_PUBLIC_DISCORD_REDIRECT_URI)}&scope=${process.env.NEXT_PUBLIC_DISCORD_SCOPE}`;

    const width = 500;
    const height = 600;
    const left = (window.innerWidth - width) / 2;
    const top = (window.innerHeight - height) / 2;

    window.open(
      discordAuthUrl,
      '_self',
      `width=${width},height=${height},top=${top},left=${left},resizable=yes,scrollbars=yes,status=yes`,
    );
  };

  const signInWithTwitch = () => {
    const clientId = process.env.NEXT_PUBLIC_TWITCH_CLIENT_ID;
    const redirectUri = process.env.NEXT_PUBLIC_TWITCH_REDIRECT_URI;
    const scope = 'openid user:read:email user:read:follows';
    console.log(redirectUri, ':::::::redirectUri');
    const authUrl = `https://id.twitch.tv/oauth2/authorize?client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&response_type=code&scope=${scope}`;

    window.location.href = authUrl;
  };


  return (
    <form onSubmit={handleSubmit}>
      <div className="grid grid-cols-1 gap-2 max-xs:grid-cols-1">
        {/* <InputField
          type="text"
          name="firstName"
          value={formData.firstName}
          placeholder="First Name"
          onChange={handleChange}
          error={formErrors.firstName}
          label="First Name"
          color
        />
        <InputField
          type="text"
          name="lastName"
          value={formData.lastName}
          placeholder="Last Name"
          onChange={handleChange}
          error={formErrors.lastName}
          label="Last Name"
          color
        /> */}
        <InputField
          type="text"
          name="username"
          value={formData.username}
          placeholder="Create your username"
          onChange={handleChange}
          error={formErrors.username}
          label="Username"
          color
        />
        <InputField
          type="text"
          name="email"
          value={formData.email}
          placeholder="Enter your email"
          onChange={handleChange}
          error={formErrors.email}
          label="Email"
          color
        />
        <InputField
          type="password"
          name="password"
          value={formData.password}
          placeholder="Enter your password"
          onChange={handleChange}
          error={formErrors.password}
          label="Password"
          color
        />
        {/* <InputField
          type="password"
          name="confirmPassword"
          value={formData.confirmPassword}
          placeholder="Enter Confirm Password"
          onChange={handleChange}
          error={formErrors.confirmPassword}
          label="Confirm Your Password"
          color
        /> */}
      </div>

      <div className="flex items-center gap-2 py-6">
        <label htmlFor='checkbox' className=''>
          <input
            type="checkbox"
            id='checkbox'
            className='bg-cetaceanBlue-2000 size-6 rounded-md border border-solid border-[#848481]  appearance-none checked:bg-checkbox-check checked:bg-center checked:bg-no-repeat checked:bg-contain'
          />
        </label>

        <p className="text-sm font-medium leading-5 text-white-1000">
          I agree to the Terms and Conditions and Privacy Policy.
        </p>
      </div>



      <ReferralCodeBox />


      {/* <div className="mt-3 flex gap-3">
        <input
          name="over18"
          id="over18"
          type="checkbox"
          checked={formData.over18}
          onChange={handleChange}
          className="border-white h-5 w-5 min-w-5 cursor-pointer appearance-none rounded border border-solid bg-transparent bg-contain bg-center bg-no-repeat checked:bg-primary-1000 checked:bg-checkbox-check"
        />
        <label
          htmlFor="over18"
          className="text-white text-[0.813rem] font-normal md:text-base !leading-tight"
        >
          I am at least 18 years old and not a resident of the restricted
          states.
        </label>
      </div> */}

      {/* <div className="mt-1 flex gap-3">
        <input
          name="acceptTerms"
          id="conditions"
          type="checkbox"
          checked={formData.acceptTerms}
          onChange={handleChange}
          className="border-white h-5 w-5 min-w-5 cursor-pointer appearance-none rounded border border-solid bg-transparent bg-contain bg-center bg-no-repeat checked:bg-primary-1000 checked:bg-checkbox-check"
        />
        <label
          htmlFor="conditions"
          className="text-white cursor-pointer text-[0.813rem] font-normal md:text-base !leading-tight"
        >
          I accept the Terms and Conditions
        </label>
      </div> */}

      {(formErrors.acceptTerms || formErrors.over18) && (
        <p className="mt-1 text-red-500">
          {formErrors.acceptTerms || formErrors.over18}
        </p>
      )}
      {/* <div className="mt-6 flex justify-center md:justify-start">
        <PrimaryButton type="submit" disabled={isLoading}>
          {isLoading ? <ButtonLoader /> : 'Sign Up'}
        </PrimaryButton>
      </div> */}

      <div className="mt-6 flex w-full flex-wrap items-center justify-between gap-2 md:justify-between max-xxs:flex-col">
        <div className="flex items-center gap-2 w-full max-xxs:flex-col max-xxs:pr-0">
          <PrimaryButton variant="secondary" type="submit" disabled={isLoading}>
            {isLoading ? <ButtonLoader /> : 'Sign Up'}
          </PrimaryButton>
        </div>
      </div>

      <span className="flex items-center pt-6 pb-6">
        <span className="h-px flex-1 bg-cetaceanBlue-2000"></span>

        <span className="text-sm uppercase font-semibold shrink-0 px-4 text-cetaceanBlue-4000">OR</span>

        <span className="h-px flex-1 bg-cetaceanBlue-2000"></span>
      </span>

      <div className="flex items-center justify-between gap-2 max-xxs:justify-center [&>button:hover]:bg-richBlack-500 [&>button]:flex [&>button]:items-center [&>button]:justify-center [&>button]:rounded-md [&>button]:bg-richBlack-1000 [&>button]:p-2 [&>button]:transition-all [&>button]:duration-200 [&>button]:ease-in-out">
        <button type="button" className='w-full'>
          <GoogleIcon />
        </button>
        <button type="button" className='w-full'>
          <FacebookIcon />
        </button>
        <button type="button" className='w-full'>
          <DiscordIcon />
        </button>
        <button type="button" className='w-full'>
          <TwitchIcon />
        </button>

      </div>
    </form >
  );
}

export default SignUp;
