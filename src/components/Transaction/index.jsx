"use client";

import { useEffect, useRef, useState } from "react";
import Select from "react-select";
import Datepicker from "tailwind-datepicker-react";
import { dateOptions } from "./DateOptions";
import { getTransactionData } from "@/actions";
import Pagination from "../Pagination";
import {
  convertTo24HourTime,
  formatDate,
  formatDateMDY,
  getDateDaysAgo,
} from "@/utils/customizedDate";
import Loader from "../Loader";

const options = [
  { value: "all", label: "All" },
  { value: "addGc", label: "Add GC" },
  { value: "addSc", label: "Add SC" },
  // { value: 'add', label: 'SC Coin' },
];

function Transaction() {
  const [showStartDate, setShowStartDate] = useState(false);
  const [showEndDate, setShowEndDate] = useState(false);
  const [startDate, setStartDate] = useState(new Date(getDateDaysAgo(10)));
  const [endDate, setEndDate] = useState(new Date());
  const [selectedCoin, setSelectedCoin] = useState({
    value: "all",
    label: "All",
  });
  const [dateError, setDateError] = useState("");
  const [transactionData, setTransactionData] = useState([]);
  const [count, setCount] = useState(0);
  const [page, setPage] = useState(1);
  const [errorMessage, setErrorMessage] = useState("");
  const [loading, setLoading] = useState(false);
  const limit = 6;

  const fetchTransactionData = async (params) => {
    setLoading(true);
    try {
      const response = await getTransactionData(params);
      console.log("****************response Transactions", response);
      if (response?.data?.data?.count === 0) {
        setTransactionData([]);
        setErrorMessage("No record Found!");
      } else {
        const transData = response?.data?.data;
        setTransactionData(transData?.rows || []);
        setCount(transData?.count || 0);
        setErrorMessage("");
      }
    } catch (error) {
      setErrorMessage("Something went wrong while fetching data.");
      console.error("Error fetching transaction data:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    let params = {
      startDate: formatDate(startDate),
      endDate: formatDate(endDate),
      page: page,
      limit: limit,
      actionType: selectedCoin?.value,
    };
    console.log("************params", params);
    fetchTransactionData(params);
  }, [startDate, endDate, selectedCoin, page]);

  const handleStartDateChange = (date) => {
    if (date && endDate && date >= endDate) {
      setDateError("End date must be after start date.");
      setEndDate(new Date());
    } else {
      setStartDate(date);
      setDateError("");
    }
  };

  const handleEndDateChange = (date) => {
    if (startDate && date.getTime() < startDate.getTime()) {
      setDateError("End date must be after start date.");
    } else {
      setEndDate(date);
      setDateError("");
    }
  };

  const handleDateClose = (state, type) => {
    if (type === "start") {
      setShowStartDate(state);
    } else if (type === "end") {
      setShowEndDate(state);
    }
  };

  const getDateOptions = (type, defaultDate) => {
    return {
      ...dateOptions,
      defaultDate: defaultDate,
      minDate:
        type === "end" ? startDate || dateOptions.minDate : dateOptions.minDate,
      disabledDates:
        type === "end" && startDate
          ? [new Date(startDate.getTime() - 86400000)]
          : [],
    };
  };

  const handlePageChange = (page) => {
    setPage(page);
  };

  const totalPages = Math.ceil(count / limit);
  const showPagination = transactionData?.length > 0 && totalPages > 1;
  // let startDateval = new Date(startDate)
  return (
    <div className="py-[50px] max-xl:py-6 px-5 max-xxs:px-3">
      <div className="flex flex-col md:flex-row md:space-x-6 max-md:space-y-6 max-md:space-x-0">
        <div className="">
          <div className="relative">
            <Datepicker
              options={getDateOptions("start", startDate)}
              onChange={handleStartDateChange}
              show={showStartDate}
              setShow={(state) => handleDateClose(state, "start")}
              className="date-picker"
              placeholder="Start Date"
            ></Datepicker>
            <div className='absolute bottom-0 h-[3px] w-full bg-blackOpacity-100 after:content-[""] after:absolute after:h-[3px] after:w-0 after:bg-borderBg peer-focus:after:w-full after:transition-all after:duration-300 after:ease-linear' />
          </div>
        </div>
        <div className="">
          <div className="relative">
            <Datepicker
              options={getDateOptions("end", endDate)}
              onChange={handleEndDateChange}
              show={showEndDate}
              setShow={(state) => handleDateClose(state, "end")}
              className="date-picker"
              placeholder="End Date"
              disabled={!startDate}
            />
            <div className='absolute bottom-0 h-[3px] w-full bg-blackOpacity-100 after:content-[""] after:absolute after:h-[3px] after:w-0 after:bg-borderBg peer-focus:after:w-full after:transition-all after:duration-300 after:ease-linear' />
          </div>
          {dateError && (
            <p className="text-red-500 mt-2 text-[12px]">{dateError}</p>
          )}
        </div>
        <div className="w-full flex-1">
          <div className="relative">
            <Select
              onChange={setSelectedCoin}
              options={options}
              value={options.filter(
                (option) => option.value === selectedCoin?.value
              )}
              placeholder="Select Coin"
              className="coin-select"
              classNamePrefix="coin-select-inner"
              isSearchable={false}
            />
          </div>
        </div>
      </div>
      <div className="mt-6">
        <div className="overflow-x-auto min-h-[100px]">
          {loading ? (
            <Loader />
          ) : (
            <table className="min-w-full divide-y-2 divide-darkGray-300 bg-transparent">
              <thead>
                <tr>
                  <th className="whitespace-nowrap px-4 py-[9px] font-bold text-sm capitalize text-primary-400 text-start">
                    Transaction ID
                  </th>
                  <th className="whitespace-nowrap px-4 py-[9px] font-bold text-sm capitalize text-primary-400 text-start">
                    Date & Time
                  </th>
                  <th className="whitespace-nowrap px-4 py-[9px] font-bold text-sm capitalize text-primary-400 text-start">
                    Gold Coin
                  </th>
                  <th className="whitespace-nowrap px-4 py-[9px] font-bold text-sm capitalize text-primary-400 text-start">
                    Sweep Coin
                  </th>
                  <th className="whitespace-nowrap px-4 py-[9px] font-bold text-sm capitalize text-primary-400 text-start">
                    Type
                  </th>
                  {/*                 <th className="whitespace-nowrap px-4 py-[9px] font-bold text-sm capitalize text-primary-400 text-start">Method</th>
                <th className="whitespace-nowrap px-4 py-[9px] font-bold text-sm capitalize text-primary-400 text-start">Action</th> */}
                </tr>
              </thead>
              <tbody className="divide-y divide-darkGray-300 text-base">
                {Object.keys(transactionData).length > 0 ? (
                  transactionData?.map((data) => {
                    console.log(data, ":::::data");
                    return (
                      <tr key={data.transactionid}>
                        <td className="whitespace-nowrap px-4 py-3 font-normal text-darkGray-1000">
                          {data?.transactionid || data?.id}
                        </td>
                        <td className="whitespace-nowrap px-4 py-3 font-normal text-darkGray-1000">
                          {formatDateMDY(data.updated_at)} , {convertTo24HourTime(data.updated_at)}
                        </td>
                        <td className="whitespace-nowrap px-4 py-3 font-normal text-darkGray-1000">
                          {data.gccoin || 0}
                        </td>
                        <td className="whitespace-nowrap px-4 py-3 font-normal text-warningRed-1000">
                          {data.sccoin || 0}
                        </td>

                        <td className="whitespace-nowrap px-4 py-3 font-normal text-warningRed-1000">
                          {data.transactiontype == "addAc"
                            ? "add sc"
                            : "add gc"}
                        </td>
                        <td className="whitespace-nowrap px-4 py-3 font-normal text-warningRed-1000">
                          {data.method}
                        </td>
                      </tr>
                    );
                  })
                ) : (
                  <tr>
                    <td
                      colSpan={8}
                      className="px-4 py-3 font-normal text-darkGray-1000 text-center"
                    >
                      {errorMessage}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          )}
        </div>
        {showPagination && (
          <Pagination
            className="mt-4"
            totalPages={totalPages}
            currentPage={page}
            onPageChange={handlePageChange}
          />
        )}
      </div>
    </div>
  );
}

export default Transaction;
