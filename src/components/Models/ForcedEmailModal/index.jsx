'use client';

import React from 'react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import InputField from '@/components/Common/InputField'; // Assuming InputField is compatible with react-hook-form
import { zodResolver } from '@hookform/resolvers/zod';
import { ForcedEmailSchema } from '@/schemas/ForcedEmailSchema';
import { useFacebookLoginMutation } from '@/reactQuery/authQuery';
import IconButton from '@/components/Common/Button/IconButton';
import useModalStore from '@/store/useModalStore';
import { X, ChevronLeft, ChevronRight } from 'lucide-react';
function ForcedEmailModal({ userData }) {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm({
    resolver: zodResolver(ForcedEmailSchema),
    defaultValues: {
      email: '',
    },
  });
  const { closeModal, openModal } = useModalStore((state) => state);
  const facebookMutation = useFacebookLoginMutation({
    onSuccess: (response) => {
      console.log(response, ":::::::::facebook response");
    },
    onError: (error) => {
      console.log(error, "::::::::::::facebook error")
    }
  })


  const onSubmit = async (data) => {
    try {
      // Handle email submission logic here
      console.log(data); // Log the email data
      userData.email = data?.email;
      userData.isForceEmail = true;
      toast.success('Email submitted successfully!');
      facebookMutation.mutate(userData);
      if (res?.data?.data?.user) {
        // setIsLoggedIn(true);
        // setUser(res?.data?.data?.user);
        closeModal()
      } else {
        toast.error(res?.message || "Failed to login with Facebook..");
        // setLoading(false);
      }
    } catch (error) {
      toast.error('Failed to submit email!');
    }
  };

  return (
    <>
      <div
        tabIndex="-1"
        aria-hidden="true"
        className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto"
      >
        <div className="relative w-full max-w-xl p-4">
          <div className="rounded-lg bg-maastrichtBlue-1000 shadow-lg">
            <div className="flex items-center justify-between p-4">
              <div className="flex items-center gap-3">

                <h3 className="text-white mt-1 text-lg font-semibold leading-none tracking-wide">
                  User Info
                </h3>
              </div>

              <IconButton
                onClick={() => closeModal()}
                className="h-6 w-6 min-w-6"
              >
                <X className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
              </IconButton>
            </div>
            {/* {loading ? (
              <div className="text-white p-4">Loading...</div>
            ) : ( */}
            <div className="p-4">
              <div className="mb-4 flex items-center justify-between gap-4">
                <div className="flex items-center gap-2">
                  <div className="relative">
                    <form onSubmit={handleSubmit(onSubmit)}>
                      <div className="grid grid-cols-1 gap-2">
                        <div>
                          <label
                            className="mb-1 flex gap-2 text-base font-normal text-steelTeal-1000 relative"
                          >
                           User Email
                            
                          </label>
                          <input
                          type="email"
                          {...register('email')}
                          placeholder="User Email"
                          className={`text-white w-full rounded-md border ${errors.email?.message ? 'border-red-500' : 'border-solid border-richBlack-1000'
                            } bg-richBlack-1000 p-3 pr-10 text-base font-normal focus:border-steelTeal-1000 focus:shadow-inputInsetShadow focus:outline-none`}
                          // error={errors.email?.message}
                          
                        />
                         {errors.email?.message && <p className="mt-1 text-xs text-red-500">{errors.email?.message}</p>}
                        </div>
                        
                      </div>
                      <div className="mt-6 flex justify-center">
                        <PrimaryButton type="submit" disabled={isSubmitting}>
                          {isSubmitting ? 'Submitting...' : 'Submit'}
                        </PrimaryButton>
                      </div>
                    </form>
                  </div>
                </div></div></div>
            {/* )} */}
          </div></div></div>
    </>
    // <div className='bg-black-500'>
    //   <form onSubmit={handleSubmit(onSubmit)}>
    //     <div className="grid grid-cols-1 gap-2">
    //       <InputField
    //         type="email"
    //         {...register('email')}
    //         placeholder="User Email"
    //         error={errors.email?.message}
    //         label="User Email"
    //       />
    //     </div>
    //     <div className="mt-6 flex justify-center">
    //       <PrimaryButton type="submit" disabled={isSubmitting}>
    //         {isSubmitting ? 'Submitting...' : 'Submit'}
    //       </PrimaryButton>
    //     </div>
    //   </form>
    // </div>
  );
}

export default ForcedEmailModal;


