'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { UserPlus } from 'lucide-react'; // Join group icon
import {
  useGetFriendsListQuery,
  useJoinedGroupMutation,
} from '@/reactQuery/chatWindowQuery';
import { toast } from 'react-hot-toast';
import Tooltip from '@/components/Common/Tooltip';
import IconButton from '@/components/Common/Button/IconButton';
import { X, UserRoundMinus } from 'lucide-react';
import useModalStore from '@/store/useModalStore';
import { useQueryClient } from '@tanstack/react-query';
import defaultImage from '@/assets/icons/profile-icon.svg';

function AddFriendsFromSearch({ groupId, groupMembers }) {
  const [search, setSearch] = useState('');
  const [selectedMembers, setSelectedMembers] = useState([]);

  const { data: friendsList } = useGetFriendsListQuery({
    enabled: !!search,
    params: { search },
  });
  const { closeModal } = useModalStore();
  const queryClient = useQueryClient();
  const { mutate: updateJoinedGroup, isPending } = useJoinedGroupMutation({
    onSuccess: () => {
      // console.log(response, ":::::::::::this is a respopnse i need to check")
      // toast.success('Successfully joined the group!');
      queryClient.invalidateQueries(['GET_GROUP_LIST_QUERY']);
      closeModal();
    },
    onError: (error) => {
      const message =
        error.response?.data?.errors?.[0]?.description ||
        'Something went wrong';
      toast.error(message);
    },
  });

  const handleJoinedGroup = () => {
    const payload = {
      groupId,
      action: 'add',
      members: selectedMembers,
    };

    updateJoinedGroup(payload, {
      onSuccess: () => {
        toast.success('Successfully added to the group!');
      },
    });
  };

  const handleMemberSelect = (userId) => {
    setSelectedMembers((prev) => {
      if (prev.includes(userId)) {
        return prev.filter((id) => id !== userId); // If the user is already selected, remove them
      }
      return [...prev, userId]; // Otherwise, add them to the selection
    });
  };

  const handleRemoveMember = (userId) => {
    const payload = {
      groupId,
      action: 'remove',
      members: [userId],
    };

    updateJoinedGroup(payload, {
      onSuccess: () => {
        toast.success('Successfully removed from the group!');
      },
    });
  };

  return (
    <div
      tabIndex="-1"
      aria-hidden="true"
      className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto"
    >
      <div className="relative w-full max-w-xl p-4">
        <div className="rounded-lg bg-maastrichtBlue-1000 shadow-lg">
          <div className="flex items-center justify-between p-4">
            <h3 className="text-white mt-1 text-lg font-semibold">
              Add and Remove Member in Group
            </h3>
            <IconButton
              onClick={() => closeModal()}
              className="h-6 w-6 min-w-6"
            >
              <X className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
            </IconButton>
          </div>
          <div className="px-2 py-1">
            <div className="mb-1 flex items-center gap-2 bg-maastrichtBlue-1000 px-[0.625rem] py-1">
              <input
                className="h-10 w-full resize-none rounded-[0.625rem] bg-cetaceanBlue-1000 px-[0.625rem] py-2 placeholder:text-steelTeal-1000"
                placeholder="Search friends"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
              />
            </div>
            <section className="max-h-[218px] overflow-y-auto pt-1">
              <div className="flex flex-col gap-2">
                {search && friendsList?.rows?.length > 0 ? (
                  friendsList.rows.map((friend) => {
                    const isMember = groupMembers.some(
                      (member) => member.userId === friend.relationUser?.userId,
                    );
                    const isSelected = selectedMembers.includes(
                      friend.relationUser?.userId,
                    ); // Check if this user is selected

                    return (
                      <div
                        key={friend.relationUser?.userId}
                        className="flex items-center justify-between gap-[0.625rem] rounded-xl border border-oxfordBlue-1000 bg-maastrichtBlue-1000 p-2"
                      >
                        <div className="flex flex-row gap-2.5">
                          <div className="relative h-12 w-12 min-w-12 cursor-pointer rounded-full border-2 border-oxfordBlue-1000">
                            <Image
                              src={defaultImage}
                              width={10000}
                              height={10000}
                              className="h-full w-full max-w-full rounded-full object-cover object-center"
                              alt="Profile"
                            />
                          </div>
                          <div className="mt-4 flex justify-center">
                            {friend.relationUser?.username}
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          {isMember ? (
                            <div
                              className="cursor-pointer"
                              onClick={() =>
                                handleRemoveMember(friend.relationUser?.userId)
                              }
                            >
                              <UserRoundMinus className="text-white h-6 w-6" />
                            </div>
                          ) : isSelected ? (
                            <div
                              className="cursor-pointer"
                              onClick={() =>
                                handleMemberSelect(friend.relationUser?.userId)
                              }
                            >
                              <UserRoundMinus className="text-white h-6 w-6" />
                            </div>
                          ) : (
                            <Tooltip text="Add to group" position="left">
                              <UserPlus
                                className="text-white h-6 w-6 cursor-pointer"
                                onClick={() =>
                                  handleMemberSelect(
                                    friend.relationUser?.userId,
                                  )
                                }
                              />
                            </Tooltip>
                          )}
                        </div>
                      </div>
                    );
                  })
                ) : (
                  <div className="mt-10 flex justify-center">No Friends</div>
                )}
              </div>
            </section>
            <button
              type="button"
              onClick={handleJoinedGroup}
              disabled={selectedMembers.length === 0 || isPending}
              className="text-white mt-4 w-full rounded-lg bg-steelTeal-1000 p-2 disabled:opacity-50"
            >
              {isPending ? 'Joining...' : 'Join Group'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default AddFriendsFromSearch;
