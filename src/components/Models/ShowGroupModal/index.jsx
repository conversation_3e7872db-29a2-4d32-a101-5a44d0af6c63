'use client';

import IconButton from '@/components/Common/Button/IconButton';
import useModalStore from '@/store/useModalStore';
import { UserRoundPlus, X, UserRoundMinus, Loader2 } from 'lucide-react';
import { useState, useEffect } from 'react';
import AddFriendsFromSearch from './AddFriendsFromSearch';
import defaultImage from '@/assets/icons/profile-icon.svg'
import Image from 'next/image';
import useDebounce from '@/utils/useDebounce';
import { useGetGroupDetails, useJoinedGroupMutation } from '@/reactQuery/chatWindowQuery';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import UpdateGroupDetailsModal from '../UpdateGroupDetailsModal';
import useGroupChatStore from '@/store/useGroupChatStore';
import useAuthStore from '@/store/useAuthStore';

const ShowGroupModal = ({   }) => {

  const { groupId } = useGroupChatStore(
    (state) => state,
  );
  const { isAuthenticated, userDetails } = useAuthStore((state) => state);
  const { data: groupDetails, refetch: refetchGroupDetails, loading, isLoading } =
    useGetGroupDetails({ params: { groupId }, enabled: (!!isAuthenticated) });


  const [searchTerm, setSearchTerm] = useState('');
  const [filteredMembers, setFilteredMembers] = useState([]);

  const debouncedSearchTerm = useDebounce(searchTerm, 800); // Apply debounce with 800ms delay

  const { openModal, closeModal } = useModalStore();
  const queryClient = useQueryClient();
  
  const openAddMemberModal = () => {
    openModal(<AddFriendsFromSearch 
      groupId={groupId} 
      groupMembers={groupDetails?.group?.members || []} 
      isAdmin={userDetails?.userId === groupDetails?.group?.groupAdmin} 
    />);
  };

  const { mutate: updateJoinedGroup, isLoading: isRemoving } = useJoinedGroupMutation({
    onSuccess: (response) => {
      toast.success('Successfully removed from group')
      
      queryClient.invalidateQueries(['GET_GROUP_LIST_QUERY']);
      closeModal()
    },
    onError: (error) => {
      const message =
        error.response?.data?.errors?.[0]?.description || 'Something went wrong';
      toast.error(message);
    },
  });

  // Initialize filteredMembers when groupDetails changes
  useEffect(() => {
    if (groupDetails?.group?.members) {
      setFilteredMembers(groupDetails.group.members);
    }
  }, [groupDetails]);

  // Filter members based on debounced search term
  useEffect(() => {
    if (!groupDetails?.group?.members) return;
    
    if (debouncedSearchTerm) {
      const filtered = groupDetails.group.members.filter((member) => {
        if (!member?.user) return false;
        
        const { firstName = '', lastName = '', username = '' } = member.user;
        return (
          firstName.toLowerCase().includes(debouncedSearchTerm.toLowerCase()) ||
          lastName.toLowerCase().includes(debouncedSearchTerm.toLowerCase()) ||
          username.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
        );
      });
      setFilteredMembers(filtered);
    } else {
      setFilteredMembers(groupDetails.group.members); // Reset to all members if no search term
    }
  }, [debouncedSearchTerm, groupDetails?.group?.members]); // Dependency array to trigger when debounced term changes

  const handleRemoveMember = (userId) => {
    console.log(`Removing member with userId: ${userId}`);
    // Your logic to remove the member
    const payload = {
      groupId, 
      action: 'remove', 
      members: [userId], 
    };

    updateJoinedGroup(payload);
  };

  const openEditGroupDetails = () => {
    openModal(<UpdateGroupDetailsModal 
      groupId={groupId} 
      groupMembers={groupDetails?.group?.members || []} 
    />);
  }

  const isAdmin = userDetails?.userId === groupDetails?.group?.groupAdmin;
  const canUpdateGroupDetails = groupDetails?.group?.groupSettings?.onlyAdminCanUpdateGroupDetails === false || isAdmin;
  console.log('!!!!!!!!!',groupDetails?.group);
  
  const canAddMembers = groupDetails?.group?.groupSettings?.onlyAdminCanAddMembers === false || isAdmin;
  const isDataLoading = loading || isLoading;

  return (
    <div
      tabIndex="-1"
      aria-hidden="true"
      className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto"
    >
      <div className="relative w-full max-w-xl p-4">
        <div className="rounded-lg bg-maastrichtBlue-1000 shadow-lg">
          <div className="flex items-center justify-between p-4">
            <h3 className="text-white mt-1 text-lg font-semibold">
              Members of {groupDetails?.group?.groupName || 'Group'}
            </h3>
          {isDataLoading ?  <Loader2 className="h-8 w-8 animate-spin text-steelTeal-1000" /> :(  <div className='flex flex-row gap-4 items-center'>
              {canUpdateGroupDetails && <span onClick={openEditGroupDetails} className="cursor-pointer">Update details</span>}
              {canAddMembers && <IconButton onClick={openAddMemberModal} className="h-6 w-6 min-w-6">
                <UserRoundPlus className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
              </IconButton>}
              <IconButton onClick={closeModal} className="h-6 w-6 min-w-6">
                <X className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
              </IconButton>
            </div>)}
          </div>
          <div className="px-2 py-1">
            <div className="mb-1 flex items-center gap-2 bg-maastrichtBlue-1000 px-[0.625rem] py-1">
              <input
                className="h-10 w-full resize-none rounded-[0.625rem] bg-cetaceanBlue-1000 px-[0.625rem] py-2 placeholder:text-steelTeal-1000"
                placeholder="Search friends"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                disabled={isDataLoading}
              />
            </div>

            <section className="max-h-[218px] overflow-y-auto pt-1">
              <div className="flex flex-col gap-2">
                {isDataLoading ? (
                  <div className="flex justify-center items-center py-10">
                    <Loader2 className="h-8 w-8 animate-spin text-steelTeal-1000" />
                    <span className="ml-2 text-steelTeal-1000">Loading members...</span>
                  </div>
                ) : filteredMembers?.length > 0 ? (
                  filteredMembers.map((member) => {
                    if (!member?.user) return null;
                    
                    const { firstName = '', lastName = '', username = '', profileImage = '' } = member.user;
                    const profile_img = profileImage || defaultImage;
                    const userId = member?.userId;
                    const memberIsAdmin = member?.isAdmin;
                    
                    return (
                      <div
                        key={userId}
                        className="flex items-center justify-between gap-[0.625rem] rounded-xl border border-oxfordBlue-1000 bg-maastrichtBlue-1000 p-2"
                      >
                        <div className="flex flex-row justify-center items-center gap-2.5">
                          {/* Profile Image */}
                          <div
                            className="relative h-12 w-12 min-w-12 cursor-pointer rounded-full border-2 border-oxfordBlue-1000"
                            onClick={() => console.log("Profile clicked")}
                          >
                            <Image
                              src={profile_img}
                              width={10000}
                              height={10000}
                              className="h-full w-full max-w-full rounded-full object-cover object-center"
                              alt="Profile"
                            />
                          </div>

                          {/* User Info (Name and Admin check) */}
                          <div className="mt-4 flex flex-col justify-center">
                            <span className="font-semibold">{firstName} {lastName}</span>
                            {memberIsAdmin && <span className="text-sm text-green-500">Admin</span>}
                            <span className="text-sm text-gray-500">@{username}</span>
                          </div>
                        </div>
                        {!memberIsAdmin && canAddMembers && (
                          <div 
                            className={`cursor-pointer ${isRemoving ? 'opacity-50' : ''}`} 
                            onClick={() => !isRemoving && handleRemoveMember(userId)}
                          >
                            {isRemoving ? (
                              <Loader2 className="h-6 w-6 text-white animate-spin" />
                            ) : (
                              <UserRoundMinus className="h-6 w-6 text-white" />
                            )}
                          </div>
                        )}
                      </div>
                    );
                  })
                ) : (
                  <div className="mt-10 flex justify-center text-steelTeal-1000">No members found</div>
                )}
              </div>
            </section>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ShowGroupModal;