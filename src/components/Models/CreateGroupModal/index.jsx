'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import IconButton from '@/components/Common/Button/IconButton';
import { zodResolver } from '@hookform/resolvers/zod';
import { CreateGroupSchema } from '@/schemas/CreateGroupSchema';
import useModalStore from '@/store/useModalStore';
import { X } from 'lucide-react';
import { useGroupChatMutation } from '@/reactQuery/chatWindowQuery';
import { useQueryClient } from '@tanstack/react-query';
import Image from 'next/image';
import MultiSelect from '@/components/ChatSelect';
import GroupImg from '../../../../public/assets/demo-image/group-img.jpg';

function ToggleSwitch({ label, name, register }) {
  return (
    <div className="flex items-center justify-between">
      <span className="text-white text-base">{label}</span>
      <label className="relative inline-flex cursor-pointer items-center">
        <input type="checkbox" {...register(name)} className="peer sr-only" />
        <div className="after:bg-white peer-focus:ring-steelTeal-300 relative h-6 w-11 rounded-full bg-gray-600 after:absolute after:left-1 after:top-1 after:h-4 after:w-4 after:rounded-full after:border after:border-gray-300 after:transition-all after:content-[''] peer-checked:bg-steelTeal-1000 peer-checked:after:translate-x-5 peer-focus:ring-4" />
      </label>
    </div>
  );
}

function CreateGroupModal() {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm({
    resolver: zodResolver(CreateGroupSchema),
    defaultValues: {
      groupName: '',
      groupDescription: '',
      onlyAdminCanCall: false,
      onlyAdminCanAddMembers: false,
      onlyAdminCanUpdateGroupDetails: false,
    },
  });
  const [selectedUsers, setSelectedUsers] = useState([]); // Manage selected users' IDs

  const queryClient = useQueryClient();
  const { closeModal } = useModalStore();

  const groupChatMutation = useGroupChatMutation({
    onSuccess: (response) => {
      queryClient.invalidateQueries(['GET_GROUP_LIST_QUERY']);
    },
    onError: (error) => {
      const message =
        error.response?.data?.errors?.[0]?.description || 'Failed to sign up';
      // setError(message);
      toast.error(message);
    },
  });

  const onSubmit = async (data) => {
    try {
      console.log(selectedUsers, ':::::::selectedUsers'); // Log the form data
      data.onlyAdminCanCall = String(data.onlyAdminCanCall);
      data.onlyAdminCanAddMembers = String(data.onlyAdminCanAddMembers);
      data.onlyAdminCanUpdateGroupDetails = String(
        data.onlyAdminCanUpdateGroupDetails,
      );
      data.members = selectedUsers;
      groupChatMutation.mutate(data);
      closeModal();
    } catch (error) {
      toast.error('Failed to create group!');
    }
  };

  return (
    <div
      tabIndex="-1"
      aria-hidden="true"
      className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto"
    >
      <div className="relative w-full max-w-xl p-4">
        <div className="rounded-lg bg-maastrichtBlue-1000 shadow-lg">
          <div className="flex items-center justify-between p-4">
            <h3 className="text-white mt-1 text-lg font-semibold">
              Create Group
            </h3>
            <IconButton
              onClick={() => closeModal()}
              className="h-6 w-6 min-w-6"
            >
              <X className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
            </IconButton>
          </div>
          <div className="p-4 max-xl:pb-16">
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="">
                <div className="mb-4 flex items-center gap-4">
                  <div>
                    <label htmlFor="group-img" className="">
                      <Image
                        src={GroupImg}
                        className="bg-white h-20 w-20 rounded-full object-cover object-center"
                      />
                      <input type="file" id="group-img" className="hidden" />
                    </label>
                  </div>
                  <div className="grow">
                    <label className="mb-1 text-base font-normal text-steelTeal-1000">
                      Enter group name
                    </label>
                    <input
                      type="text"
                      {...register('groupName')}
                      placeholder="Enter group name"
                      className={`w-full rounded-md border ${errors.groupName ? 'border-red-500' : 'border-solid border-richBlack-1000'} text-white bg-black-1000 px-3 py-2 focus:border-steelTeal-1000 focus:shadow-inputInsetShadow focus:outline-none`}
                    />
                    <div className="text-end text-[10px] text-steelTeal-1000">
                      0/100
                    </div>
                    {errors.groupName && (
                      <p className="mt-0.5 text-xs text-red-500">
                        {errors.groupName.message}
                      </p>
                    )}
                  </div>
                </div>

                <MultiSelect
                  className="chatMulti-select"
                  classNamePrefix="chatMulti-inner-select"
                  onChange={setSelectedUsers}
                />

                <div className="mt-3">
                  <label className=" text-base font-normal text-steelTeal-1000">
                    Group Description
                  </label>
                  <textarea
                    {...register('groupDescription')}
                    placeholder="Enter group description"
                    className={`w-full rounded-md border ${errors.groupDescription ? 'border-red-500' : 'border-solid border-richBlack-1000'} text-white bg-black-1000 p-3 focus:border-steelTeal-1000 focus:shadow-inputInsetShadow focus:outline-none`}
                  />
                  {errors.groupDescription && (
                    <p className="mt-1 text-xs text-red-500">
                      {errors.groupDescription.message}
                    </p>
                  )}
                </div>
                <div className="mt-4 flex flex-col gap-3">
                  <ToggleSwitch
                    label="Only Admin Can Call"
                    name="onlyAdminCanCall"
                    register={register}
                  />
                  <ToggleSwitch
                    label="Only Admin Can Add Members"
                    name="onlyAdminCanAddMembers"
                    register={register}
                  />
                  <ToggleSwitch
                    label="Only Admin Can Update Group Details"
                    name="onlyAdminCanUpdateGroupDetails"
                    register={register}
                  />
                </div>
              </div>
              <div className="mt-6 flex justify-center">
                <PrimaryButton type="submit" disabled={isSubmitting}>
                  {isSubmitting ? 'Creating...' : 'Create Group'}
                </PrimaryButton>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}

export default CreateGroupModal;
