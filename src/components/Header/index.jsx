'use client';

import React, { useEffect, useCallback, memo } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useSearchParams, usePathname, useRouter } from 'next/navigation';
import useAuthStore from '@/store/useAuthStore';
import { toast } from 'react-hot-toast';
import { PartyPopper } from 'lucide-react';
import { useUserProfileQuery } from '@/reactQuery/authQuery';
import { useQueryClient } from '@tanstack/react-query';
import { chatRoomSocket, livePlayerSocket, walletSocket } from '@/utils/socket';
import HamburgerIcon from '@/assets/icons/Hamburger';
import useGeneralStore from '@/store/useGeneralStore';
import useModalStore from '@/store/useModalStore';
import useAuthTab from '@/store/useAuthTab';
import useSignUp from '@/hooks/useSignUp';
import { getAccessToken, removeAccessToken } from '@/utils/helper';
import useVoiceCallStore from '@/store/useVoiceCallStore';
import useAudioPlayer from '@/utils/useAudioPlayer';
import useCallStore from '@/store/useCallStore';
import useCallModalStore from '@/store/useCallModalStore';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import useGreenBonusStore from '@/store/useGreenBonusStore';
import { useGreenBonusClaim } from '@/reactQuery/gamesQuery';
import CallPopup from '../Models/CallPopup';
import Auth from '../Auth';
import PrimaryButtonOutline from '../Common/Button/PrimaryButtonOutline';
import PrimaryButton from '../Common/Button/PrimaryButton';
import brandLogo from '../../assets/webp/logo.webp';
import CasinoIcon from '@/assets/icons/CasinoIcon';
import BetIcon from '@/assets/icons/BetIcon';
import SportIcon from '@/assets/icons/SportIcon';
import LobbyIcon from '@/assets/icons/LobbyIcon';
import ChatIcon from '@/assets/icons/Chat';

const MOBILE_BUTTON_CLASSES = 'max-md:min-h-8 !w-fit';
const MIN_WIDE_SCREEN_WIDTH = 1280;

function Header() {
  const { isAuthenticated, setUserDetails, setUserWallet, hasRehydrated } =
    useAuthStore();
  const accessToken = getAccessToken();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const queryClient = useQueryClient();
  const router = useRouter();
  const { audio, playAudio } = useAudioPlayer();
  const { setCallStartTime, setCallDuration, setToggleMuted } = useCallStore();
  const { closeModal: closeCallModal, openModal: openCallModal } =
    useCallModalStore((state) => state);
  const { setVoiceCall } = useVoiceCallStore((state) => state);

  const { twitchMutation, discordMutation } = useSignUp();
  const { setSelectedTab } = useAuthTab();
  const { openModal } = useModalStore((state) => state);
  const { isCallActive, setIsCallActive } = usePrivateChatStore(
    (state) => state,
  );
  const { setGreenBonusData } = useGreenBonusStore((state) => state);
  const { openChat, setOpenChat, setOpeMenu, openMenu } = useGeneralStore();

  const { data: userProfile } = useUserProfileQuery({
    enabled: isAuthenticated,
  });

  useEffect(() => {
    const code = searchParams.get('code');

    let timeout;

    if (code) {
      timeout = setTimeout(() => {
        if (pathname === '/discord-login') {
          const payload = {
            code,
            isSignup: true,
          };
          discordMutation.mutate(payload);
        } else if (pathname === '/twitch-signup') {
          const payload = {
            code,
            isSignup: true,
          };
          twitchMutation.mutate(payload);
        }
      }, 500); // 500ms delay to prevent quick re-triggers
    }

    return () => clearTimeout(timeout);
  }, [pathname, searchParams]);

  useEffect(() => {
    if (userProfile) {
      setUserDetails(userProfile);
      const defaultWallet = userProfile?.wallets?.find(
        (wallet) => wallet.default,
      );
      setUserWallet(defaultWallet);
    }
  }, [userProfile, setUserDetails, setUserWallet]);

  const cliamGreenBonusMutation = useGreenBonusClaim({
    onSuccess: (response) => {
      toast('Bonus Claimed Successfully!', {
        icon: <PartyPopper size={20} />,
      });
    },
    onError: (error) => {
      const message =
        error.response?.data?.errors?.[0]?.description ||
        'Failed to claim green bonus';
      setError(message);
      toast.error(message);
    },
  });

  const isClaimGreenBonusCalled = (data) => {
    cliamGreenBonusMutation.mutate(data);
  };

  const handleWalletUpdate = useCallback(
    (data) => {
      if (data?.inActive) {
        toast.error(data?.message);
        removeAccessToken();
        localStorage.clear();
        queryClient.removeQueries();
        router.push('/');
      }
      setUserWallet(data);
    },
    [setUserWallet],
  );

  const onChatRoomCall = (data) => {
    // openModal(<CallPopup />)
    setCallStartTime(null), setCallDuration(0);
    setToggleMuted(false);
    playAudio();
    openCallModal(<CallPopup />);
    setVoiceCall({
      channelName: data?.data?.channelName,
      role: data?.data?.role,
      callLogId: data?.data?.callLogId,
      userId: data?.data?.userId,
      username: data?.data?.username,
      profileImage: data?.data?.profileImage,
    });
  };

  const onDeclineCall = async () => {
    try {
      audio.pause();
      audio.currentTime = 0;
      if (!isCallActive) {
        closeCallModal();
        return;
      }
      rtc.localAudioTrack?.close();
      await rtc.client.leave();
      setIsCallActive(false);
    } catch (error) {
      console.error('Error disconnecting call:', error);
    } finally {
      closeCallModal(); // Add this to close the modal in all scenarios
      // audio.pause();
      // audio.currentTime = 0;
    }
  };
  const onRemainingthresold = (data) => {
    setGreenBonusData(data.data);
  };

  // Helper function to safely connect a socket if it's not already connected
  const safelyConnectSocket = useCallback((socket) => {
    if (socket && !socket.connected) {
      socket.connect();
    }
  }, []);

  useEffect(() => {
    if (isAuthenticated) {
      // Set auth tokens first
      walletSocket.auth = { token: accessToken };
      chatRoomSocket.auth = { token: accessToken };

      // Safely connect sockets only if they're not already connected
      safelyConnectSocket(walletSocket);
      safelyConnectSocket(livePlayerSocket);
      safelyConnectSocket(chatRoomSocket);

      const handleChatRoomCall = (data) => onChatRoomCall(data);
      const handleDeclineCall = (data) => onDeclineCall(data);
      const handleRemainingThreshold = (data) => onRemainingthresold(data);
      const handleClaimGreenBonus = (data) => isClaimGreenBonusCalled(data);

      // Remove existing listeners before adding new ones to prevent duplicates
      chatRoomSocket.off('PERSONAL_VOICE_CHAT_CHANNEL');
      chatRoomSocket.off('DECLINE_PERSONAL_VOICE_CHAT_CHANNEL');
      walletSocket.off('REMAINING_THRESHOLD');
      walletSocket.off('wallet');

      // Add event listeners
      chatRoomSocket.on('PERSONAL_VOICE_CHAT_CHANNEL', handleChatRoomCall);
      chatRoomSocket.on(
        'DECLINE_PERSONAL_VOICE_CHAT_CHANNEL',
        handleDeclineCall,
      );
      walletSocket.on('REMAINING_THRESHOLD', handleRemainingThreshold);
      walletSocket.on('wallet', handleWalletUpdate);
      walletSocket.on('CLAIM_GREEN_BONUS', handleClaimGreenBonus);

      return () => {
        // Only disconnect if connected
        if (walletSocket.connected) walletSocket.disconnect();
        if (livePlayerSocket.connected) livePlayerSocket.disconnect();
        if (chatRoomSocket.connected) chatRoomSocket.disconnect();

        // Remove all listeners
        chatRoomSocket.off('PERSONAL_VOICE_CHAT_CHANNEL');
        chatRoomSocket.off('DECLINE_PERSONAL_VOICE_CHAT_CHANNEL');
        walletSocket.off('REMAINING_THRESHOLD');
        walletSocket.off('wallet');
      };
    }
  }, [isAuthenticated, handleWalletUpdate, safelyConnectSocket, accessToken]);

  useEffect(() => {
    const handleResize = () => {
      const isWideScreen = window.matchMedia(
        `(min-width: ${MIN_WIDE_SCREEN_WIDTH}px)`,
      ).matches;
      setOpenChat(isWideScreen);
    };

    handleResize();
    window.addEventListener('resize', handleResize);

    return () => window.removeEventListener('resize', handleResize);
  }, [setOpenChat]);

  const handleAuth = useCallback(
    (tab) => {
      setSelectedTab(tab);
      localStorage.setItem('activeTab', tab);
      openModal(<Auth />);
    },
    [setSelectedTab, openModal],
  );

  if (!hasRehydrated) return null;

  const headerClasses = `
    header-blur fixed border-b border-solid border-white-200 left-0 top-0 z-50 flex h-headerHeight w-full items-center justify-center bg-oxfordBlue-1000 px-3 transition-all duration-300 ease-in-out xs:px-8 xl:z-40 xl:ml-sidebarWidth xl:shadow-header
    ${isAuthenticated ? '' : '!z-[42]'}
    ${openChat
      ? 'xl:mr-chatWidth xl:w-[calc(100%-var(--sidebar-wdith)-var(--chat-width))]'
      : 'xl:mr-[0rem] xl:w-[calc(100%-var(--sidebar-wdith))]'
    }
  `.trim();

  return (
    <header className={headerClasses}>
      <div className={` flex justify-between items-center max-w-containerWidth w-full`}>
        <Link href="/" className="inline-block">
          <Image
            src={brandLogo}
            width={10000}
            height={10000}
            className="w-full max-w-[9.75rem]"
            alt="Brand Logo"
            priority
          />
        </Link>

        <div className='flex items-center gap-5 justify-center border-t border-solid border-white-200 max-lg:hidden'>
          <a href='#' className='text-center text-xs font-semibold text-steelTeal-200 capitalize px-3 py-1'>
            <CasinoIcon className='size-6 mx-auto mb-1' />
            casino
          </a>
          <a href='#' className='text-center text-xs font-semibold text-steelTeal-200 capitalize px-3 py-1'>
            <BetIcon className='size-6 mx-auto mb-1' />
            Bets
          </a>
          <a href='#' className='text-center text-xs font-semibold text-steelTeal-200 capitalize px-3 py-1'>
            <BetIcon className='size-6 mx-auto mb-1' />
            Sports
          </a>
          <a href='#' className='text-center text-xs font-semibold text-steelTeal-200 capitalize px-3 py-1'>
            <CasinoIcon className='size-6 mx-auto mb-1' />
            Lobby
          </a>
          <a href='#' className='text-center text-xs font-semibold text-steelTeal-200 capitalize px-3 py-1'>
            <ChatIcon className='size-6 mx-auto mb-1' />
            Chats
          </a>

        </div>

        {!isAuthenticated && (
          <div className="flex items-center justify-end">
            <div className="flex w-full items-center justify-end gap-2">
              <PrimaryButtonOutline
                className={MOBILE_BUTTON_CLASSES}
                onClick={() => handleAuth(1)}
              >
                Login
              </PrimaryButtonOutline>
              <PrimaryButton
                className={MOBILE_BUTTON_CLASSES}
                onClick={() => handleAuth(0)}
                variant="secondary"
              >
                Register
              </PrimaryButton>
            </div>
          </div>
        )}

        <button
          type="button"
          onClick={() => setOpeMenu(!openMenu)}
          className="h-6 w-6 items-center justify-center hidden"
          aria-label="Toggle menu"
        >
          <HamburgerIcon className="h-full w-full fill-white-750" />
        </button>
      </div>
    </header>
  );
}

Header.displayName = 'Header';

export default Header;
