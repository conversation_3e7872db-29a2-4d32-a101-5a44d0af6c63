/* eslint-disable no-nested-ternary */
'use client';

import React, { useState, useEffect } from 'react';
// import Image from 'next/image';
import toast from 'react-hot-toast';
import {
  UserRoundMinus,
  UserRoundPlus,
  Eye,
  EyeOff,
  MessageSquareShare,
  HandCoins,
  X,
} from 'lucide-react';
import useUserActions from '@/hooks/useUserActions';
import HeartFillIcon from '@/assets/icons/Heart-Fill';
import HeartStrokeIcon from '@/assets/icons/Heart-Stroke';
import useUserInfoStore from '@/store/useUserInfoStore';
import useUserDetails from '@/hooks/useUserDetails';
// import coinAC from '@/assets/images/stock-images/coin-ac.png';
import ProfileIcon from '@/assets/icons/Profile';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import useAuthStore from '@/store/useAuthStore';
import {
  useCreateFriendsRequest,
  useUnFriendsRequest,
} from '@/reactQuery/chatWindowQuery';
import useModalStore from '@/store/useModalStore';
import { usePathname } from 'next/navigation';
import defaultImage from '@/assets/icons/profile-icon.svg'
import IconButton from '../Common/Button/IconButton';
import CustomImage from '../Common/CustomImage';
import Tooltip from '../Common/Tooltip';
import StoreModal from '../Store';

// function CurrencySwitcher({ currency, setCurrency }) {
//   return (
//     <div className="flex gap-2 rounded-full bg-cetaceanBlue-1000 p-2">
//       <button
//         type="button"
//         className={`flex items-center gap-2 rounded-full pr-2.5 ${currency === 'AC' ? 'bg-oxfordBlue-1000 text-white-1000' : 'text-steelTeal-1000'}`}
//         onClick={() => setCurrency('AC')}
//       >
//         <Image
//           src={coinAC}
//           width={10000}
//           height={10000}
//           className="h-auto w-6 max-w-full xl:w-7"
//           alt="Coin"
//         />
//         <span className="mt-0.5 inline-block text-sm leading-none tracking-wider">
//           AC
//         </span>
//       </button>
//     </div>
//   );
// }

// function StatsCard({ label, value }) {
//   return (
//     <div className="flex min-h-14 flex-col items-center justify-center gap-1.5 rounded-lg bg-cetaceanBlue-1000 p-2.5">
//       <h5 className="text-white font-semi-bold mb-auto text-base leading-none">
//         {value}
//       </h5>
//       <h6 className="mt-auto text-sm leading-none tracking-wider text-steelTeal-1000">
//         {label}
//       </h6>
//     </div>
//   );
// }

function UserInfo({
  setUsername = () => {},
  setPrivateChatUserDetails = () => {},
}) {
  const pathname = usePathname();
  const checkIfGamePath = () => {
    if (pathname.startsWith('/game') || pathname.startsWith('/crash-game')) {
      toast.error('Can not access store while playing game');
      return true;
    }
    return false;
  };

  const { openModal, closeModal } = useModalStore((state) => state);
  // const [currency, setCurrency] = useState('AC');
  const {
    // isUserInfoModalOpen,
    selectedUserId,
    closeUserInfoModal,
    userDetails,
    loading,
    setUserDetails,
  } = useUserInfoStore();
  const { setIsPrivateChatOpen, setUserId } = usePrivateChatStore(
    (state) => state,
  );
  const { refetch, data: privateChatDetails } = useUserDetails();
  const userId = useAuthStore((state) => state?.userDetails?.id);
  const { ignoreUser, likeUser, unignoreUser, unlikeUser } = useUserActions({
    refetch,
  });

  const [isLiked, setIsLiked] = useState(false);
  const [isIgnored, setIsIgnored] = useState(false);

  const mutationRequest = useCreateFriendsRequest({
    onSuccess: (response) => {
      toast.success(response?.data?.message);
      userDetails.friendRequestStatus = 'pending';
      setUserDetails(userDetails);
      refetch();
    },
    onError: (error) => {
      toast.error(error.response.data.errors.map((e) => e.description));
      refetch();
    },
  });

  const mutationUnFriendRequest = useUnFriendsRequest({
    onSuccess: (response) => {
      toast.success(response?.data?.message);
      refetch();
    },
    onError: (error) => {
      toast.error(error.response.data.errors.map((e) => e.description));
      refetch();
    },
  });

  const unFriend = (unfriendUserId) => {
    mutationUnFriendRequest.mutate({ unfriendUserId });
  };

  useEffect(() => {
    if (userDetails) {
      setIsLiked(userDetails.liked);
      setIsIgnored(userDetails.ignored);
    }
  }, [userDetails]);

  useEffect(() => {
    if (privateChatDetails) {
      setPrivateChatUserDetails(privateChatDetails);
    }
  }, [privateChatDetails]);

  const handleLike = () => {
    if (isLiked) {
      unlikeUser(selectedUserId);
    } else {
      likeUser(selectedUserId);
    }
    setIsLiked(!isLiked);
  };

  const handleIgnore = () => {
    if (isIgnored) {
      unignoreUser(selectedUserId);
    } else {
      ignoreUser(selectedUserId);
    }
    setIsIgnored(!isIgnored);
  };

  const openChat = () => {
    console.log("🚀 ~ openChat ~ userDetails?.userId:", userDetails)
    setUserId(userDetails?.id);
    setIsPrivateChatOpen(true);
    closeUserInfoModal();
    setUsername('');
    closeModal();
  };

  const handleOpenTip = (userDetails) => {
    openModal(<StoreModal userDetails={userDetails} currentActiveTab="tips" />);
  };
  return (
    <div
      tabIndex="-1"
      aria-hidden="true"
      className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto"
    >
      <div className="relative w-full max-w-xl p-4">
        <div className="rounded-lg bg-maastrichtBlue-1000 shadow-lg">
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center gap-3">
              <ProfileIcon className="h-5 w-5 fill-white-1000" />
              <h3 className="text-white mt-1 text-lg font-semibold leading-none tracking-wide">
                User Info
              </h3>
            </div>

            <IconButton
              onClick={() => closeModal()}
              className="h-6 w-6 min-w-6"
            >
              <X className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
            </IconButton>
          </div>
          {loading ? (
            <div className="text-white p-4">Loading...</div>
          ) : (
            <div className="p-4">
              <div className="mb-4 flex items-center justify-between gap-4">
                <div className="flex items-center gap-2">
                  <div className="relative h-20 w-20 min-w-20 rounded-full border-2 border-oxfordBlue-1000">
                    <CustomImage
                      src={userDetails?.profileImage || defaultImage}
                      alt="Profile"
                      width={80}
                      height={80}
                      className="mr-4 h-full w-full rounded-full"
                      skeletonWidth={80}
                      skeletonHeight={80}
                    />
                    {/* <span className="absolute -bottom-0.5 -left-1 flex h-7 w-7 items-center justify-center rounded-full border-2 border-oxfordBlue-1000 bg-primary-1000 pt-1 text-center text-xs leading-none">
                      14
                    </span> */}
                  </div>

                  <div className="flex flex-col gap-2">
                    <h5 className="text-white text-lg leading-none">
                      {userDetails?.username}
                    </h5>
                    {/* <h6 className="rounded-full bg-cetaceanBlue-1000 px-2.5 py-1.5 text-sm leading-none text-steelTeal-1000">
                      Level: 2
                    </h6> */}
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  {userDetails?.id !== userId && (
                    <Tooltip text="Chat" position="top">
                      <MessageSquareShare
                        onClick={openChat}
                        className="cursor-pointer text-steelTeal-1000 hover:text-white-1000"
                      />
                    </Tooltip>
                  )}
                  {/* {userDetails?.id !== userId && (
                    <Tooltip text="Give Tips" position="top">
                      <HandCoins
                        onClick={() =>
                          !checkIfGamePath() && handleOpenTip(userDetails)
                        }
                        className="cursor-pointer text-steelTeal-1000 hover:text-white-1000"
                      />
                    </Tooltip>
                  )} */}
                  {userDetails?.id !== userId ? (
                    userDetails?.friendRequestStatus ? (
                      <Tooltip text="Un-Friend" position="top">
                        <UserRoundMinus
                          onClick={() =>
                            unFriend(userDetails && userDetails.userId)
                          }
                          className="cursor-pointer text-steelTeal-1000 hover:text-white-1000"
                        />
                      </Tooltip>
                    ) : (
                      <Tooltip text="Add-Friend" position="top">
                        <UserRoundPlus
                          onClick={() =>
                            mutationRequest.mutate({
                              requesteeId: userDetails && userDetails.id,
                            })
                          }
                          className="cursor-pointer text-steelTeal-1000 hover:text-white-1000"
                        />
                      </Tooltip>
                    )
                  ) : null}
                  {userDetails?.id !== userId && (
                    <button type="button" onClick={handleIgnore} className="">
                      {isIgnored ? (
                        <Tooltip text="Unignore-User" position="top">
                          <EyeOff className="text-steelTeal-1000 transition-all duration-300 hover:text-white-1000" />
                        </Tooltip>
                      ) : (
                        <Tooltip text="Ignore-User" position="top">
                          <Eye className="text-steelTeal-1000 transition-all duration-300 hover:text-white-1000" />
                        </Tooltip>
                      )}
                    </button>
                  )}

                  {userDetails?.id !== userId && (
                    <div className="flex items-center justify-center gap-3 rounded-full bg-cetaceanBlue-1000 p-2">
                      <button onClick={handleLike} type="button" className="">
                        {isLiked ? (
                          <Tooltip text="Unlike" position="top">
                            <HeartFillIcon className="h-5 w-5 fill-primary-1000 transition-all duration-300" />
                          </Tooltip>
                        ) : (
                          <Tooltip text="Like" position="top">
                            <HeartStrokeIcon className="h-5 w-5 fill-primary-1000 transition-all duration-300" />
                          </Tooltip>
                        )}
                      </button>
                      <span className="mt-0.5 inline-block text-sm leading-none text-steelTeal-1000">
                        {userDetails?.likesCount}
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* <div className="mb-4 flex items-center justify-end gap-4">
                <CurrencySwitcher
                  currency={currency}
                  setCurrency={setCurrency}
                />
              </div> */}

              {/* <div className="grid grid-cols-2 gap-2 text-center">
                <StatsCard
                  label="WINS"
                  value={
                    currency === 'AC' ? userDetails?.ACWin : userDetails?.GCWin
                  }
                />
                <StatsCard
                  label="LOSSES"
                  value={
                    currency === 'AC'
                      ? userDetails?.ACLost
                      : userDetails?.GCLost
                  }
                />
                <StatsCard
                  label="BETS"
                  value={
                    currency === 'AC' ? userDetails?.ACBet : userDetails?.GCBet
                  }
                />
              </div> */}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default UserInfo;
