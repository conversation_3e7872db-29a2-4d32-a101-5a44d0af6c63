'use client';

// import React, { useState } from 'react';
import Select from 'react-select';



function TransactionFilter({ selectedOption, setSelectedOption, options }) {
  return (
    <div className="flex w-full  max-w-[250px] flex-col">
      {/* <label className="text-sm font-medium text-darkGray-1000">
                Filter
            </label> */}
      <Select
        defaultValue={selectedOption}
        onChange={setSelectedOption}
        options={options}
        className="form-input-select"
        classNamePrefix="form-input"
      />
    </div>
  );
}

export default TransactionFilter;
