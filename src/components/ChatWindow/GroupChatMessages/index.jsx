'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';
import Image from 'next/image';
import { <PERSON><PERSON>, <PERSON>c<PERSON><PERSON>, ChevronLeft } from 'lucide-react';
import AgoraRTC from 'agora-rtc-sdk-ng';
import { v4 as uuidv4 } from 'uuid';

// Custom components
import CustomImage from '@/components/Common/CustomImage';
import CloseIcon from '@/assets/icons/CloseIcon';
import CallIcon from '@/assets/icons/CallIcon';
import DisconnectIcon from '@/assets/icons/DisconnectIcon';
import defaultImage from '@/assets/icons/profile-icon.svg';

// Hooks and utilities
import usePrivateChatStore from '@/store/usePrivateChatStore';
import useAuthStore from '@/store/useAuthStore';
import useVoiceCallStore from '@/store/useVoiceCallStore';
import useModalStore from '@/store/useModalStore';
import useGroupChatStore from '@/store/useGroupChatStore';
import {
  useDeclined<PERSON><PERSON>,
  useGenerateAgoraToken,
  useGetGroupDetails,
} from '@/reactQuery/chatWindowQuery';
import { formatDateTime, getAccessToken, isValidURL } from '@/utils/helper';
import { chatRoomSocket, voiceCallConnected } from '@/utils/socket';
import IconButton from '../../Common/Button/IconButton';

// Initialize Agora client once outside component to prevent recreation
const rtc = {
  client: AgoraRTC.createClient({ mode: 'rtc', codec: 'h264' }),
  localAudioTrack: null,
  remoteAudioTrack: null,
};

// Set log level once
AgoraRTC.setLogLevel(4);

function GroupChatConversion({ privateChat }) {
  // Store hooks
  const { isGroupChatOpen, setIsGroupChatOpen, groupId, groupChat } =
    useGroupChatStore();
  const { isCallActive, setIsCallActive } = usePrivateChatStore();
  const { userDetails, isAuthenticated } = useAuthStore();
  const { setVoiceCall, voiceCall } = useVoiceCallStore();
  const { clearModals } = useModalStore();

  // Local state
  const [channelName, setChannelName] = useState('');
  const [isMuted, setIsMuted] = useState(false);

  // Refs
  const chatContainerRef = useRef(null);
  const retryCounterRef = useRef(0);
  const TOKEN_GEN_RETRY_LIMIT = 3;

  // Fetch group details
  const { data: groupDetails } = useGetGroupDetails({
    params: { groupId },
    enabled: !!isAuthenticated && !!groupId,
  });

  // Handler functions

    // Decline call mutation
    const declineCall = useDeclinedCall({
      onSuccess: () => clearModals(),
      onError: (error) => console.error('Error declining call:', error),
    });
  
  const disconnectCall = useCallback(
    async (data) => {
      try {
        const payload = {
          channelName,
          callLogId: voiceCall?.callLogId,
          isOneToOneCall: 'true',
        };

        if (!isCallActive) {
          payload.notAttended = 'true';
        }

        await declineCall.mutate(payload);

        // Clean up resources
        rtc.localAudioTrack?.close();
        await rtc.client.leave();
        setIsCallActive(false);
      } catch (error) {
        console.error('Error disconnecting call:', error);
      }
    },
    [channelName, voiceCall, isCallActive, declineCall, setIsCallActive],
  );

  // Generate unique channel name on mount
  useEffect(() => {
    setChannelName(uuidv4());
  }, []);

  // Setup socket connection
  useEffect(() => {
    const accessToken = getAccessToken();

    // Setup socket connections
    chatRoomSocket.auth = { token: accessToken };
    chatRoomSocket.connect();

    voiceCallConnected.auth = { token: accessToken };
    voiceCallConnected.connect();

    // Listen for call decline events
    chatRoomSocket.on('DECLINE_PERSONAL_VOICE_CHAT_CHANNEL', disconnectCall);

    // Cleanup on unmount
    return () => {
      chatRoomSocket.off('DECLINE_PERSONAL_VOICE_CHAT_CHANNEL');
    };
  }, []);

  // Setup Agora client event listeners
  useEffect(() => {
    if (!rtc.client) return;

    const handleUserPublished = async (user, mediaType) => {
      await rtc.client.subscribe(user, mediaType);
      if (mediaType === 'audio') {
        rtc.remoteAudioTrack = user.audioTrack;
        rtc.remoteAudioTrack.play();
      }
    };

    const handleUserUnpublished = (user, mediaType) => {
      if (mediaType === 'audio' && rtc.remoteAudioTrack) {
        rtc.remoteAudioTrack.stop();
        rtc.remoteAudioTrack = null;
      }
    };

    rtc.client.on('user-published', handleUserPublished);
    rtc.client.on('user-unpublished', handleUserUnpublished);

    return () => {
      rtc.client.off('user-published', handleUserPublished);
      rtc.client.off('user-unpublished', handleUserUnpublished);
      setIsMuted(false);
    };
  }, []);

  // Auto-scroll chat to bottom when messages change
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop =
        chatContainerRef.current.scrollHeight;
    }
  }, [privateChat, groupChat]);


  // Generate Agora token mutation
  const tokenGenration = useGenerateAgoraToken({
    onSuccess: async (response) => {
      // Update call log ID in store
      setVoiceCall({ ...voiceCall, callLogId: response?.data?.callLogId });

      // Join the channel
      await rtc.client.join(
        process.env.NEXT_PUBLIC_AGORA_APP_ID,
        channelName,
        response.data.token,
        userDetails.uniqueId,
      );

      // Create and publish audio track
      rtc.localAudioTrack = await AgoraRTC.createMicrophoneAudioTrack();
      await rtc.client.publish(rtc.localAudioTrack);

      // Emit connected event
      chatRoomSocket.emit('USER_VOICE_CALL_CONNECTED', {
        isConnected: true,
        callLogId: response?.data?.callLogId,
        channelName,
        groupId,
        userId: userDetails.uniqueId,
        token: response.data.token,
      });

      setIsCallActive(true);
    },
    onError: (error) => {
      console.error('Error generating token:', error);

      // Retry logic
      if (retryCounterRef.current < TOKEN_GEN_RETRY_LIMIT) {
        retryCounterRef.current += 1;

        tokenGenration.mutate({
          channelName,
          role: 'publisher',
          groupId,
        });
      } else {
        console.log('Max retry limit reached. No further retries.');
      }
    },
  });

  const toggleMicrophone = useCallback(async () => {
    if (rtc.localAudioTrack) {
      await rtc.localAudioTrack.setEnabled(!isMuted);
      setIsMuted(!isMuted);
    }
  }, [isMuted]);

  const initiateCall = useCallback(() => {
    tokenGenration.mutate({
      channelName,
      role: 'publisher',
      groupId,
    });
  }, [channelName, groupId, tokenGenration]);

  const closePrivateChatModal = useCallback(() => {
    setIsGroupChatOpen(false);
  }, [setIsGroupChatOpen]);

  // Check if user can manage calls
  const canManageCalls =
    groupDetails?.group?.groupSettings?.onlyAdminCanAddMembers === false ||
    userDetails?.userId === groupDetails?.group?.groupAdmin;

  // Render null if group chat is not open
  if (!isGroupChatOpen) return null;

  return (
    <div className="flex h-full w-full flex-col">
      <div className="flex items-center justify-between gap-2 bg-oxfordBlue-1000 px-4 py-2 shadow-chat-header">
        <div className="flex w-full items-center justify-between">
          <ChevronLeft
            className="cursor-pointer text-steelTeal-1000 transition-all duration-300 hover:text-white-1000"
            onClick={closePrivateChatModal}
          />
          <div className="flex items-center gap-4 rounded-full border-2 border-oxfordBlue-1000">
            <CustomImage
              src={groupDetails?.group?.profile || defaultImage}
              alt="Profile"
              width={40}
              height={40}
              className="h-full w-full max-w-full rounded-full object-cover object-center"
              skeletonWidth={40}
              skeletonHeight={40}
            />
            <p className="text-xl">{groupDetails?.group?.groupName}</p>
          </div>
          <div className="flex items-center gap-2">
            {canManageCalls && (
              <>
                <IconButton
                  onClick={isCallActive ? disconnectCall : initiateCall}
                  className="h-6 w-6"
                >
                  {isCallActive ? (
                    <DisconnectIcon className="h-5 w-5 fill-red-600 transition-all duration-300 hover:fill-red-800" />
                  ) : (
                    <CallIcon className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 hover:fill-white-1000" />
                  )}
                </IconButton>

                <IconButton onClick={toggleMicrophone} className="h-6 w-6">
                  {isMuted ? (
                    <MicOff className="h-5 w-5 text-red-500 transition-all duration-300 hover:text-red-600" />
                  ) : (
                    <Mic className="h-5 w-5 text-steelTeal-1000 transition-all duration-300 hover:text-white-1000" />
                  )}
                </IconButton>
              </>
            )}
            <IconButton onClick={closePrivateChatModal} className="h-6 w-6">
              <CloseIcon className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
            </IconButton>
          </div>
        </div>
      </div>

      <div className="flex min-h-0 shrink grow basis-[0%] flex-col">
        <div
          ref={chatContainerRef}
          className="scrollbar-none flex min-h-0 shrink grow basis-[0%] flex-col overflow-y-auto overflow-x-hidden px-[0.625rem]"
        >
          <div className="flex flex-col gap-[0.625rem]">
            {groupChat?.map((chat) => {
              const isReceived =
                Number(chat?.userId) !== Number(userDetails?.userId);

              return (
                <div
                  key={chat?.createdAt}
                  className={`flex ${isReceived ? '' : 'justify-end'} gap-[0.625rem] py-[0.625rem] pl-[0.625rem]`}
                >
                  <div className="flex w-[calc(100%_-_3.625rem)] flex-col gap-3">
                    <div className="flex w-full flex-col gap-1">
                      <h6
                        className={`flex ${isReceived ? '' : 'justify-end'} gap-[0.625rem] text-[0.813rem] font-normal leading-none text-steelTeal-1000`}
                      >
                        {isReceived && (
                          <span className="inline-block truncate">
                            @{chat?.user?.username}
                          </span>
                        )}
                        <span className="inline-block">
                          {formatDateTime(chat?.createdAt)}
                        </span>
                      </h6>

                      {chat?.message &&
                        (isValidURL(chat?.message) ? (
                          <Image
                            src={chat?.message}
                            width={10000}
                            height={10000}
                            className="w-32 rounded-lg rounded-tl-none bg-maastrichtBlue-1000 px-3 py-2 text-sm font-normal text-white-1000"
                            alt="GIF"
                          />
                        ) : (
                          <p className="rounded-lg rounded-tl-none bg-maastrichtBlue-1000 px-3 py-2 text-sm font-normal text-white-1000">
                            {chat?.message}
                          </p>
                        ))}

                      {chat?.image && (
                        <Image
                          src={chat?.image}
                          width={10000}
                          height={10000}
                          className="h-auto w-full max-w-full"
                          alt="Chat Image"
                        />
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}

export default GroupChatConversion;
