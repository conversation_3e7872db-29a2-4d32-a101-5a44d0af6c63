'use client';

import Image from 'next/image';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import GroupImg1 from '../../../assets/images/demo-image/user-profile.jpg';
import GroupImg2 from '../../../assets/images/stock-images/user-01.png';
import MessageIcon from '@/assets/icons/MessageIcon';
import CallIcon from '@/assets/icons/CallIcon';
import AddGroupIcon from '@/assets/icons/AddGroupIcon';
import useModalStore from '@/store/useModalStore';
import CreateGroupModal from '@/components/Models/CreateGroupModal';
import { useGetGroupListQuery } from '@/reactQuery/chatWindowQuery';
import ShowGroupModal from '@/components/Models/ShowGroupModal';
import IconButton from '@/components/Common/Button/IconButton';
import useGroupChatStore from '@/store/useGroupChatStore';
import useAuthStore from '@/store/useAuthStore';
import { useQueryClient } from '@tanstack/react-query';
const images = [GroupImg1, GroupImg1, GroupImg1, GroupImg1];

const debounce = (func, delay) => {
  let timeoutId;
  return (...args) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    timeoutId = setTimeout(() => {
      func(...args);
    }, delay);
  };
};

const GroupChat = () => {
  const { openModal } = useModalStore((state) => state);
  const [searchInput, setSearchInput] = useState('');
  const [debouncedSearch, setDebouncedSearch] = useState('');
  const {
    isAuthenticated,
    userDetails
  } = useAuthStore((state) => state);
  const { setIsGroupChatOpen, setGroupId,setGroupChat,setGroupName } = useGroupChatStore(
    (state) => state,
  );
  // Create a stable debounced search function
  const debouncedSearchHandler = useMemo(
    () => debounce((value) => setDebouncedSearch(value), 1000),
    []
  );

  useEffect(() => {
    setGroupChat([])
  }, [])
  

  // Handle search input changes
  const handleSearchChange = (e) => {
    const value = e.target.value;
    setSearchInput(value);
    debouncedSearchHandler(value);
  };

  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    status,
    refetch
  } = useGetGroupListQuery({ search: debouncedSearch,enabled:(!!isAuthenticated) });


  const queryClient = useQueryClient();

  const observerRef = useRef(null);
  const lastGroupElementRef = useCallback((node) => {
    if (observerRef.current) observerRef.current.disconnect();
    observerRef.current = new IntersectionObserver(entries => {
      if (entries[0].isIntersecting && hasNextPage && !isFetchingNextPage) {
        fetchNextPage();
      }
    }, { rootMargin: '200px' });

    if (node) observerRef.current.observe(node);
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const openCreateModal = () => {
    openModal(<CreateGroupModal />);
  };

  if (status === 'pending') {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="text-white">Loading...</div>
      </div>
    );
  }

  if (status === 'error') {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="text-red-500">Error loading groups</div>
      </div>
    );
  }

  const handleOpenModal = (e,id,group) => {
    e.stopPropagation();
    openProfileDetails(id,group?.groupName)
    openModal(<ShowGroupModal groupId={id} group={group}/>);
  };

  const openChat = (groupId,groupName) => {    
    queryClient.invalidateQueries({ queryKey: ['GET_GROUP_CHATS_QUERY'] });
    setGroupId(groupId);
    setGroupName(groupName);
    setIsGroupChatOpen(true);
  };

  const openProfileDetails = (groupId,groupName) => {
    setGroupId(groupId);
    setGroupName(groupName);
  };

  return (
    <div className="px-2.5 pt-3 pb-2">
      <div className="flex items-center justify-between gap-2">
        <input
          className="h-9 max-w-[232px] w-full resize-none rounded-3xl bg-maastrichtBlue-1000 px-[0.625rem] py-2 placeholder:text-steelTeal-1000"
          placeholder="Search group"
          value={searchInput}
          onChange={handleSearchChange}
        />

        <button
          className="flex items-center justify-center px-2 gap-0.5 h-9 w-[74px] bg-primary-1000 rounded-3xl leading-none text-[10px] text-white font-bold capitalize"
          onClick={openCreateModal}
        >
          <AddGroupIcon className="w-5 h-5 grow shrink basis-full" />
          <span>create group</span>
        </button>
      </div>

      <div className="mt-3">
        {data?.groups.length === 0 ? (
          <div className="text-center text-white">No groups found</div>
        ) : (
          data?.groups.map((group, index) => {
            return (
              <div
                key={group.id}
                ref={index === data.groups.length - 1 ? lastGroupElementRef : null}
                className="flex cursor-pointer items-center justify-between gap-[0.625rem] rounded-xl border border-oxfordBlue-1000 bg-maastrichtBlue-1000 pt-2 pb-4 pl-[18px] pr-2 mb-2"
              >
                <div className="relative">
                  <div className="w-12 h-12 rounded-full border-2 border-oxfordBlue-900 overflow-hidden flex flex-wrap bg-white-1000"
                    onClick={(e) => handleOpenModal(e,group.id,group)}>
                    {images.map((imgSrc, imgIndex) => (
                      <Image
                        key={imgIndex}
                        src={imgSrc}
                        alt={`Image ${imgIndex + 1}`}
                        className="object-contain"
                      />
                    ))}
                  </div>
                  <span className="absolute -left-1.5 bottom-[-7px] w-6 h-6 bg-scarlet-900 border border-solid border-oxfordBlue-900 rounded-full text-white text-sm font-bold flex items-center justify-center">
                    {group.members.length}
                  </span>
                </div>
                <div>
                  <span className="text-white-1000">{group.groupName}</span>
                </div>
                <div className="flex items-center gap-2">
                <IconButton onClick={() => openChat(group.id,group.groupName)} className="h-6 w-6 min-w-6">
                  <MessageIcon />
                  </IconButton>
                 {(group?.groupSettings?.onlyAdminCanCall === false || userDetails?.userId ==group?.groupAdmin) && <CallIcon />}
                </div>
              </div>
            )
          }
          )
        )}
        {isFetchingNextPage && (
          <div className="text-center text-white mt-4">Loading more groups...</div>
        )}
      </div>
    </div>
  );
};

export default GroupChat;
