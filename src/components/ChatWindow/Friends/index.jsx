import { useState } from 'react';
import toast from 'react-hot-toast';
import Image from 'next/image';
import { CircleCheck, CircleX } from 'lucide-react';
import useUserInfoStore from '@/store/useUserInfoStore';
import DeleteUserIcon from '@/assets/icons/Delete-User';
import PrivateChatIcon from '@/assets/icons/PrivateChat';
import SearchIcon from '@/assets/icons/Search';
import {
  useGetFriendsListQuery,
  useGetFriendsRequestListQuery,
  useUnFriendsRequest,
  useUpdateFriendsRequest,
} from '@/reactQuery/chatWindowQuery';
import Tooltip from '@/components/Common/Tooltip';
import profile from '@/assets/icons/profile-icon.svg';
import useAuthStore from '@/store/useAuthStore';
// import UserInfo from '@/components/UserInfoModal';

export default function Friends() {
  const [search, setSearch] = useState('');
  const { openUserInfoModal, openModal } = useUserInfoStore((state) => state);
  const { isAuthenticated } = useAuthStore((state) => state);


  const { data: friendsList, refetch: refetchFriendsList } =
    useGetFriendsListQuery({ params: { search }, enabled: (!!isAuthenticated) });
  const { data: friendsRequestList, refetch: refetchFriendsRequestList } =
    useGetFriendsRequestListQuery({enabled: (!!isAuthenticated)});

  const mutationUpdateRequest = useUpdateFriendsRequest({
    onSuccess: (response) => {
      toast.success(response?.data?.message);
      refetchFriendsList();
      refetchFriendsRequestList();
    },
    onError: (error) => {
      toast.error(error.response.data.errors.map((e) => e.description));
      refetchFriendsList();
      refetchFriendsRequestList();
    },
  });

  const mutationUnFriendRequest = useUnFriendsRequest({
    onSuccess: (response) => {
      toast.success(response?.data?.message);
      refetchFriendsList();
    },
    onError: (error) => {
      toast.error(error.response.data.errors.map((e) => e.description));
      refetchFriendsList();
    },
  });

  const handleFriendRequest = (requestId, status) => {
    mutationUpdateRequest.mutate({
      requestId,
      status,
    });
  };

  const unFriend = (unfriendUserId) => {
    mutationUnFriendRequest.mutate({ unfriendUserId });
  };

  return (
    <div className="flex h-full flex-col">
      <section className="mt-1 p-2">
        <div className="m-2 flex flex-row gap-2">
          <PrivateChatIcon className="fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
          <div className="">Friends ({friendsList?.rows?.length})</div>
        </div>

        <hr className="my-1 h-px border-0 bg-gray-200 dark:bg-gray-700" />

        <div className="mb-1 flex items-center gap-2 bg-maastrichtBlue-1000 px-[0.625rem] py-1">
          <SearchIcon className="h-[1rem] w-[1rem] fill-white-1000 transition-all duration-300" />
          <textarea
            className="h-10 w-full resize-none rounded-[0.625rem] bg-cetaceanBlue-1000 px-[0.625rem] py-2 placeholder:text-steelTeal-1000"
            placeholder="search"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
        </div>
      </section>

      <section className="h-1/2 overflow-y-auto p-2">
        <div className="flex flex-col gap-2">
          {friendsList?.rows?.length > 0 ? (
            friendsList?.rows?.map((friend) => (
              <div
                key={friend.relationUser?.id}
                className="flex items-center justify-between gap-[0.625rem] rounded-xl border border-oxfordBlue-1000 bg-maastrichtBlue-1000 p-2"
              >
                <div className="flex flex-row gap-2.5">
                  <div
                    className="relative h-12 w-12 min-w-12 cursor-pointer rounded-full border-2 border-oxfordBlue-1000"
                    onClick={() =>
                      openUserInfoModal(friend.relationUser?.id)
                      }
                  >
                    <Image
                      src={friend.relationUser.imageUrl || profile}
                      width={10000}
                      height={10000}
                      className="h-full w-full max-w-full rounded-full object-cover object-center"
                      alt="Profile"
                    />
                  </div>
                  <div className="mt-4 flex justify-center">
                    {friend.relationUser?.username} 
                  </div>
                </div>
                <Tooltip text="Un-friend" position="left">
                  <DeleteUserIcon
                    onClick={() => unFriend(friend.relationUser.id)}
                    className="w-7 cursor-pointer fill-steelTeal-1000 hover:fill-white-1000"
                    alt="UnFriend"
                  />
                </Tooltip>
              </div>
            ))
          ) : (
            <div className="mt-10 flex justify-center">No Friends</div>
          )}
        </div>
      </section>

      <section className="mt-1 p-2">
        <div className="m-2 flex flex-row gap-2">
          <PrivateChatIcon className="fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
          Application List ({friendsRequestList?.rows?.length})
        </div>
        <hr className="my-1 h-px border-0 bg-gray-200 dark:bg-gray-700" />
      </section>

      <section className="h-1/2 overflow-y-auto p-2">
        <div className="flex flex-col gap-2">
          {friendsRequestList?.rows?.length > 0 ? (
            friendsRequestList?.rows?.map((friendRequest) => (
              <div
                key={friendRequest.userFriendRequester?.id}
                className="flex items-center justify-between gap-[0.625rem] rounded-xl border border-oxfordBlue-1000 bg-maastrichtBlue-1000 p-2"
              >
                <div className="flex flex-row gap-2">
                  <div
                    className="relative h-12 w-12 min-w-12 cursor-pointer rounded-full border-2 border-oxfordBlue-1000"
                    onClick={() =>
                      openUserInfoModal(friendRequest.userFriendRequester?.id)
                    }
                  >
                    <Image
                      src={
                        friendRequest.userFriendRequester.imageUrl || profile
                      }
                      width={10000}
                      height={10000}
                      className="h-full w-full max-w-full rounded-full object-cover object-center"
                      alt="Profile"
                    />
                  </div>
                  <div className="mt-4">
                    {friendRequest.userFriendRequester?.username}
                  </div>
                </div>
                <div className="flex flex-row gap-2">
                  <Tooltip text="Accept" position="bottom">
                    <CircleCheck
                      onClick={() =>
                        handleFriendRequest(
                          friendRequest.requesterUserId,
                          'accepted',
                        )
                      }
                      className="w-7 cursor-pointer text-steelTeal-1000 hover:text-white-1000"
                    />
                  </Tooltip>
                  <Tooltip text="Reject" position="bottom">
                    <CircleX
                      onClick={() =>
                        handleFriendRequest(
                          friendRequest.requesterUserId ,
                          'rejected',
                        )
                      }
                      className="w-7 cursor-pointer text-steelTeal-1000 hover:text-white-1000"
                    />
                  </Tooltip>
                </div>
              </div>
            ))
          ) : (
            <div className="mt-10 flex justify-center">No friend requests</div>
          )}
        </div>
      </section>
    </div>
  );
}
