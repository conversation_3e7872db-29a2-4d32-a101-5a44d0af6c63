'use client';

import React, { useEffect, useRef, useState } from 'react';
import Image from 'next/image';
import AgoraRTC from 'agora-rtc-sdk-ng';
import { Mic, MicOff, ChevronLeft } from 'lucide-react';
import CloseIcon from '@/assets/icons/CloseIcon';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import useAuthStore from '@/store/useAuthStore';
import CustomImage from '@/components/Common/CustomImage';
import profile from '@/assets/icons/profile-icon.svg';
import { formatDateTime, getAccessToken, isValidURL } from '@/utils/helper';
import useAudioPlayer from '@/utils/useAudioPlayer';
import useVoiceCallStore from '@/store/useVoiceCallStore';
import {
  useDeclinedCall,
  useGenerateAgoraToken,
} from '@/reactQuery/chatWindowQuery';
import useModalStore from '@/store/useModalStore';
import CallIcon from '@/assets/icons/CallIcon';
import DisconnectIcon from '@/assets/icons/DisconnectIcon';
import { chatRoomSocket } from '@/utils/socket';
import IconButton from '../../Common/Button/IconButton';

const rtc = {
  client: AgoraRTC.createClient({ mode: 'rtc', codec: 'h264' }),
  localAudioTrack: null,
  remoteAudioTrack: null,
};
export default function PrivateChat(props) {
  const { privateChat, recipientUser, privateChatUserDetails } = props;
  const {
    isPrivateChatOpen,
    setIsPrivateChatOpen,
    isCallActive,
    setIsCallActive,
  } = usePrivateChatStore((state) => state);
  const accessToken = getAccessToken();
  const { setVoiceCall, voiceCall } = useVoiceCallStore((state) => state);
  const { userDetails } = useAuthStore((state) => state);
  const { clearModals, openModal } = useModalStore((state) => state);

  const chatContainerRef = useRef(null);

  const [isMuted, setIsMuted] = useState(false);
  const tokenGenRetryLimit = 3; // Set retry limit

  const [retryCount, setRetryCount] = useState(0); // Counter for retry attempts
  const retryCounterRef = useRef(0);
  const declineCall = useDeclinedCall({
    onSuccess: async () => {
      clearModals();
    },
    onError: (error) => {
      console.error('Error declining call:', error);
    },
  });
  const channelName = [
    userDetails?.id,
    privateChatUserDetails?.userId || recipientUser?.recipientId,
  ]
    .sort((a, b) => a - b)
    .join('-');
  const { audio } = useAudioPlayer();
  const disconnectCall = async (data) => {
    try {
      audio.pause();
      audio.currentTime = 0;
      console.log(data, ':::::::::::voiceCall', voiceCall);
      const payload = {
        channelName,
        callLogId: voiceCall?.callLogId,
        isOneToOneCall: 'true',
        // otherCallerId: privateChatUserDetails?.userId,
      };
      if (!isCallActive) {
        payload.notAttended = 'true';
      }
      await declineCall.mutate(payload);

      rtc.localAudioTrack?.close();
      await rtc.client.leave();
      // setVoiceCall(null)
      setIsCallActive(false);
    } catch (error) {
      console.error('Error disconnecting call:', error);
    } finally {
      audio.pause();
      audio.currentTime = 0;
    }
  };
  useEffect(() => {
    chatRoomSocket.auth = { token: accessToken };
    chatRoomSocket.connect();
    // voiceCallConnected.auth = { token: accessToken };
    // voiceCallConnected.connect();
    // voiceCallConnected.auth = { token: accessToken };
    // voiceCallConnected.connect();
    chatRoomSocket.on('DECLINE_PERSONAL_VOICE_CHAT_CHANNEL', disconnectCall);
    chatRoomSocket.on('NOT_ATTENDED_VOICE_CALL', disconnectCall);
  }, []);

  const toggleMicrophone = async () => {
    if (rtc.localAudioTrack) {
      if (isMuted) {
        await rtc.localAudioTrack.setEnabled(true);
      } else {
        await rtc.localAudioTrack.setEnabled(false);
      }
      setIsMuted(!isMuted);
    }
  };

  const tokenGenration = useGenerateAgoraToken({
    onSuccess: async (response) => {
      setVoiceCall({ ...voiceCall, callLogId: response?.data?.callLogId });
      await rtc.client.join(
        process.env.NEXT_PUBLIC_AGORA_APP_ID,
        channelName,
        response.data.token,
        userDetails.uniqueId,
      );

      rtc.localAudioTrack = await AgoraRTC.createMicrophoneAudioTrack();
      await rtc.client.publish(rtc.localAudioTrack);
      chatRoomSocket.emit(
        'USER_VOICE_CALL_CONNECTED',
        {
          isConnected: true,
          callLogId: response?.data?.callLogId,
          channelName,
          userId: userDetails.uniqueId,
          token: response.data.token,
        },
        (error, acknowledgement) => {
          if (error) {
            console.error('Socket emission error:', error);
            // Handle the error (e.g., show a notification to the user)
          } else {
            console.log('Call connection acknowledged:', acknowledgement);
            // Handle successful acknowledgement
          }
        },
      );
      setIsCallActive(true);
    },
    onError: (error) => {
      console.error('Error generating token:', error);
      if (retryCounterRef.current < tokenGenRetryLimit) {
        retryCounterRef.current += 1;
        setRetryCount(retryCounterRef.current); // Optionally update state
        console.log(`Retry attempt: ${retryCounterRef.current}`);

        // Call the API again if retry count hasn't exceeded limit
        tokenGenration.mutate({
          channelName,
          role: 'publisher',
          callReceiverId: privateChatUserDetails.userId,
        });
      } else {
        console.log('Max retry limit reached. No further retries.');
      }
    },
  });

  const closePrivateChatModal = () => {
    setIsPrivateChatOpen(false);
  };

  useEffect(() => {
    if (!rtc.client) return;

    rtc.client.on('user-published', async (user, mediaType) => {
      await rtc.client.subscribe(user, mediaType);
      if (mediaType === 'audio') {
        rtc.remoteAudioTrack = user.audioTrack;
        rtc.remoteAudioTrack.play();
      }
    });

    rtc.client.on('user-unpublished', (user, mediaType) => {
      if (mediaType === 'audio') {
        if (rtc.remoteAudioTrack) {
          rtc.remoteAudioTrack.stop();
          rtc.remoteAudioTrack = null;
        }
      }
    });

    return () => {
      rtc.client.removeAllListeners();
      setIsMuted(false);
    };
  }, []);

  const initiateCall = async () => {
    try {
      tokenGenration.mutate({
        channelName,
        role: 'publisher',
        callReceiverId:
          privateChatUserDetails?.userId || recipientUser?.recipientId,
      });
    } catch (error) {
      console.error('Error initiating call:', error);
    }
  };
  // const initiateRecCall = async () => {
  //   try {
  //     console.log(voiceCall.callLogId, ':::::::::voiceCall.callLogId');
  //     tokenGenration.mutate({
  //       channelName,
  //       role: 'publisher',
  //       callLogId: voiceCall.callLogId,
  //       answer: 'answer',
  //     });
  //   } catch (error) {
  //     console.error('Error initiating call:', error);
  //   }
  // };

  AgoraRTC.setLogLevel(4);

  // const closePrivateChatModal = () => {
  //   setIsPrivateChatOpen(false);
  // };

  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop =
        chatContainerRef.current.scrollHeight;
    }
  }, [privateChat]);

  // const handleScroll = useCallback(() => {
  //   if (chatContainerRef.current.scrollTop === 0 && hasNextPage) {
  //     fetchNextPage();
  //   }
  // }, [fetchNextPage, hasNextPage]);

  // useEffect(() => {
  //   const chatContainer = chatContainerRef.current;
  //   if (chatContainer) {
  //     chatContainer.addEventListener('scroll', handleScroll);
  //   }
  //   return () => {
  //     if (chatContainer) {
  //       chatContainer.removeEventListener('scroll', handleScroll);
  //     }
  //   };
  // }, [handleScroll]);

  return isPrivateChatOpen ? (
    <div className="flex h-full w-full flex-col">
      <div className="flex items-center justify-between gap-2 bg-oxfordBlue-1000 px-4 py-2 shadow-chat-header">
        <div className="flex w-full items-center justify-between">
          <ChevronLeft
            className="cursor-pointer text-steelTeal-1000 transition-all duration-300 hover:text-white-1000"
            onClick={closePrivateChatModal}
          />
          <div className="flex items-center gap-4 rounded-full border-2 border-oxfordBlue-1000">
            <CustomImage
              src={recipientUser?.recipientProfileImage || profile}
              alt="Profile"
              width={40}
              height={40}
              className="h-full w-full max-w-full rounded-full object-cover object-center"
              skeletonWidth={40}
              skeletonHeight={40}
            />
            <p className="text-xl">{recipientUser?.recipientUsername}</p>
          </div>
          <div className="flex items-center gap-2">
            {(!privateChatUserDetails?.areFriends ||
              !recipientUser?.areFriends) && (
              <>
                {!isCallActive ? (
                  // isReceivingCall ? (
                  //   <IconButton onClick={initiateRecCall} className="h-6 w-6">
                  //     <IncomingCallIcon className="h-5 w-5 fill-green-600 transition-all duration-300 hover:fill-green-800" />
                  //   </IconButton>
                  // ) : (
                  <IconButton onClick={initiateCall} className="h-6 w-6">
                    <CallIcon className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 hover:fill-white-1000" />
                  </IconButton>
                ) : (
                  // )
                  <IconButton onClick={disconnectCall} className="h-6 w-6">
                    <DisconnectIcon
                      className="h-5 w-5 fill-red-600 transition-all duration-300 hover:fill-red-800"
                      fill="red"
                    />
                  </IconButton>
                )}
                <IconButton onClick={toggleMicrophone} className="h-6 w-6">
                  {isMuted ? (
                    <MicOff className="h-5 w-5 text-red-500 transition-all duration-300 hover:text-red-600" />
                  ) : (
                    <Mic className="h-5 w-5 text-steelTeal-1000 transition-all duration-300 hover:text-white-1000" />
                  )}
                </IconButton>
              </>
            )}

            <IconButton onClick={closePrivateChatModal} className="h-6 w-6">
              <CloseIcon className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
            </IconButton>
          </div>
          {/* <IconButton
            onClick={closePrivateChatModal}
            className="h-6 w-6 min-w-6"
          >
            <CloseIcon className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
          </IconButton> */}
        </div>
      </div>

      <div className="flex min-h-0 shrink grow basis-[0%] flex-col">
        <div
          ref={chatContainerRef}
          className="scrollbar-none flex min-h-0 shrink grow basis-[0%] flex-col overflow-y-auto overflow-x-hidden px-[0.625rem]"
        >
          <div className="flex flex-col gap-[0.625rem]">
            {privateChat?.map((chat) => {
              const received = Number(chat?.id) !== Number(userDetails?.id);
              return (
                <div
                  key={chat?.createdAt}
                  className={`flex ${received ? '' : 'justify-end'} gap-[0.625rem] pl-[0.625rem] pt-[0.625rem]`}
                >
                  <div className="flex w-[calc(100%_-_3.625rem)] flex-col gap-3">
                    <div className="flex w-full flex-col gap-1">
                      <h6
                        className={`flex ${received ? '' : 'justify-end'} gap-[0.625rem] text-[0.813rem] font-normal leading-none text-steelTeal-1000`}
                      >
                        {received && (
                          <span className="inline-block truncate">
                            @{chat?.user?.username}
                          </span>
                        )}
                        <span className="inline-block">
                          {formatDateTime(chat?.createdAt)}
                        </span>
                      </h6>
                      {chat?.message ? (
                        isValidURL(chat?.message) ? (
                          <Image
                            src={chat?.message}
                            width={10000}
                            height={10000}
                            className="mb-5 w-32 rounded-lg rounded-tl-none bg-maastrichtBlue-1000 px-3 py-2 text-sm font-normal text-white-1000"
                            alt="GIF"
                          />
                        ) : (
                          <p
                            className={`rounded-lg ${received ? 'rounded-tl-none' : 'rounded-br-none'} bg-maastrichtBlue-1000 px-3 py-2 text-sm font-normal text-white-1000`}
                          >
                            {chat?.message}
                          </p>
                        )
                      ) : null}
                      {chat?.image && (
                        <Image
                          src={chat?.image}
                          width={10000}
                          height={10000}
                          className="h-auto w-full max-w-full"
                          alt="Chat Image"
                        />
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  ) : null;
}
