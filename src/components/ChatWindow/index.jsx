'use client';

import React, { useEffect, useState, useCallback, useMemo, memo } from 'react';
import Image from 'next/image';
import data from '@emoji-mart/data';
import Picker from '@emoji-mart/react';
import { Grid } from '@giphy/react-components';
import {
  SendHorizontal,
  CircleArrowDown,
  MessageCircleMore,
} from 'lucide-react';
import { useRouter } from 'next/navigation';

import useGeneralStore from '@/store/useGeneralStore';
import useUserInfoStore from '@/store/useUserInfoStore';
import useModalStore from '@/store/useModalStore';
import useAuthStore from '@/store/useAuthStore';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import useGroupChatStore from '@/store/useGroupChatStore';

import { useGrabRainDrop } from '@/hooks/useGrabRainDrop';
import { useGetGroupListQuery } from '@/reactQuery/chatWindowQuery';


import FriendsIcon from '@/assets/icons/Friends';
import SettingIcon from '@/assets/icons/Setting';
import GIFIcon from '@/assets/icons/GIF';
import EmojiIcon from '@/assets/icons/Emoji';
import ChatIcon from '@/assets/icons/Chat';
import ArrowRightIcon from '@/assets/icons/Arrow-Right';
import CloseIcon from '@/assets/icons/CloseIcon';
import PublicChatIcon from '@/assets/icons/PublicChatIcon';
import SmallChatIcon from '@/assets/icons/SmallChatIcon';
import GroupChatIcon from '@/assets/icons/GroupChatIcon';
import profile from '@/assets/icons/profile-icon.svg';
import coinAc from '@/assets/images/stock-images/coin-ac.png';
import CommonLoader from '@/assets/json/button-loader.json';
import { formatDateTime, isValidURL } from '@/utils/helper';
import Lottie from 'lottie-react';
import countryFlag from '../../assets/images/stock-images/flag-uk.png';
import coinsGift from '../../assets/images/coins-gift.png';

import GroupChat from './GroupChat';
import GroupChatConversion from './GroupChatMessages';
import ReactTooltip from '../Common/ReactTooltip';
import NotificationPopup from '../NotificationPopup';
import RainCompleteModal from '../Store/Rain/RainCompleteModal';
import IconButton from '../Common/Button/IconButton';
import Friends from './Friends';
import UserInfo from '../UserInfoModal';
import CustomImage from '../Common/CustomImage';
import RecentChat from './RecentChat';
import PrivateChat from './PrivateChat';
import useChatWindow from '../../hooks/useChatWindow';
import useGroupChatWindow from '@/hooks/useGroupChatWindow';
import Auth from '../Auth';
import useAuthTab from '@/store/useAuthTab';

const ChatHeader = memo(({ section, setSection, isAuthenticated, openModal }) => {
  const { setSelectedTab } = useAuthTab((state) => state);

  return (
    <div className="flex h-[3.75rem] items-center justify-between gap-2 bg-cetaceanBlue-1000 px-2.5 py-5 shadow-chat-header">
      <button
        type="button"
        className="font-nunito flex items-center justify-center gap-2 rounded-md border-2 border-solid border-transparent bg-transparent px-2.5 py-1.5 text-[0.813rem] text-white-1000"
      >
        <span className="d-block h-5 w-5">
          <Image
            src={countryFlag}
            width={10000}
            height={10000}
            className="w-[1.875rem] max-w-full"
            alt="Country flag"
          />
        </span>
        <span>EN</span>
      </button>
      <div className="flex justify-end gap-2">
        <div className="flex gap-4">
          <NotificationPopup />

          <ReactTooltip message="Friends" id="friends-tooltip" />
          <IconButton
            id="friends-tooltip"
            onClick={() => {
              if (!isAuthenticated) {
                localStorage.setItem('activeTab', 0);
                setSelectedTab(0);
                openModal(<Auth />);
                return;
              }
              if (section === 'Friends') setSection('PublicChat');
              else setSection('Friends');
            }}
          >
            <FriendsIcon
              className={`${
                section === 'Friends'
                  ? 'fill-white-1000'
                  : 'fill-steelTeal-1000'
              } transition-all duration-300 group-hover:fill-white-1000`}
            />
          </IconButton>
        </div>
      </div>
    </div>
  );
});
ChatHeader.displayName = 'ChatHeader';

const NavTabs = memo(
  ({ section, setSection, newMessagesCount, groupTotal, isAuthenticated }) => {
    const { setIsPrivateChatOpen } = usePrivateChatStore();
    const { setIsGroupChatOpen } = useGroupChatStore();
    const { setSelectedTab } = useAuthTab((state) => state);
    const { openModal } = useModalStore((state) => state);

    const handleTabChange = useCallback(
      (newSection) => {
        if (!isAuthenticated) {
          localStorage.setItem('activeTab', 0);
          setSelectedTab(0);
          openModal(<Auth />);
          return;
        }
        setSection(newSection);
        setIsPrivateChatOpen(false);
        setIsGroupChatOpen(false);
      },
      [setSection, setIsPrivateChatOpen, setIsGroupChatOpen, isAuthenticated],
    );

    return (
      <div className="flex items-center justify-between bg-cetaceanBlue-1000 shadow-chat-header">
        <button
          type="button"
          className={`font-nunito flex w-full items-center justify-center gap-1 border-2 border-solid border-transparent ${section === 'PrivateChat' ? 'border-b-red-1000 bg-transparent' : 'bg-maastrichtBlue-1000'} h-10 px-2.5 py-2.5 text-[0.813rem] text-white-1000`}
          onClick={() => handleTabChange('PrivateChat')}
        >
          <IconButton>
            <SmallChatIcon
              className={`${section === 'PrivateChat' ? 'fill-red-1000' : 'fill-steelTeal-1000 group-hover:fill-white-1000'} transition-all duration-300`}
            />
          </IconButton>
          <span>Chat</span>
        </button>
        <button
          type="button"
          className={`font-nunito flex w-full items-center justify-center gap-1 border-2 border-solid border-transparent ${section === 'GroupChat' ? 'border-b-red-1000 bg-transparent' : 'bg-maastrichtBlue-1000'} h-10 px-2.5 py-2.5 text-[0.813rem] text-white-1000`}
          onClick={() => handleTabChange('GroupChat')}
        >
          <IconButton>
            <GroupChatIcon
              className={`${section === 'GroupChat' ? 'fill-red-1000' : 'fill-steelTeal-1000 group-hover:fill-white-1000'} transition-all duration-300`}
            />
          </IconButton>
          <span>Group</span>
          <span
            className={`text-white flex h-6 w-6 items-center justify-center rounded-full text-xs ${section === 'GroupChat' ? 'bg-maastrichtBlue-1000' : 'bg-black-1000'}`}
          >
            {groupTotal || 0}
          </span>
          {newMessagesCount > 0 && (
            <div className="h-2 w-2 rounded-full bg-green-1000" />
          )}
        </button>
        <button
          type="button"
          className={`font-nunito flex w-full items-center justify-center gap-1 border-2 border-solid border-transparent ${section === 'PublicChat' ? 'border-b-red-1000 bg-transparent' : 'bg-maastrichtBlue-1000'} h-10 px-2.5 py-2.5 text-[0.813rem] text-white-1000`}
          onClick={() => handleTabChange('PublicChat')}
        >
          <IconButton>
            <PublicChatIcon
              className={`${section === 'PublicChat' ? 'fill-red-1000' : 'fill-steelTeal-1000 group-hover:fill-white-1000'} transition-all duration-300`}
            />
          </IconButton>
          <span>Public</span>
          {newMessagesCount > 0 && (
            <div className="h-2 w-2 rounded-full bg-green-1000" />
          )}
        </button>
      </div>
    );
  },
);
NavTabs.displayName = 'NavTabs';

const ChatMessage = memo(
  ({ chat, handleOpenUserInfoModal, handleGrab, userDetails, openModal }) => {
    if (!chat) return null;

    return (
      <div className="flex gap-[0.625rem] py-[0.625rem] pl-[0.625rem]">
        <div
          className="relative h-12 w-12 min-w-12 cursor-pointer rounded-full border-2 border-oxfordBlue-1000"
          onClick={() => handleOpenUserInfoModal(chat?.userId)}
        >
          <CustomImage
            src={chat?.user?.imageUrl || profile}
            alt="Profile"
            width={45}
            height={45}
            className="h-full w-full max-w-full rounded-full object-cover object-center"
            skeletonWidth={45}
            skeletonHeight={45}
          />
          <span className="absolute -bottom-1 -left-1.5 flex h-6 w-6 items-center justify-center rounded-full border-2 border-oxfordBlue-1000 bg-primary-1000 pt-1 text-center text-xs leading-none">
            14
          </span>
        </div>
        <div className="flex w-[calc(100%_-_3.625rem)] flex-col gap-3">
          <div className="flex w-full flex-col gap-1">
            <h6 className="flex gap-[0.625rem] text-[0.813rem] font-normal leading-none text-steelTeal-1000">
              <span className="inline-block truncate">
                @{chat?.user?.username}
              </span>
              <span className="inline-block">
                {formatDateTime(chat?.createdAt)}
              </span>
            </h6>

            {!chat?.rainDrop && !chat?.moreDetails ? (
              isValidURL(chat?.message) ? (
                <Image
                  src={chat?.message}
                  width={10000}
                  height={10000}
                  className="w-32 rounded-lg rounded-tl-none bg-maastrichtBlue-1000 px-3 py-2 text-sm font-normal text-white-1000"
                  alt="GIF"
                />
              ) : (
                <p className="rounded-lg rounded-tl-none bg-maastrichtBlue-1000 px-3 py-2 text-sm font-normal text-white-1000">
                  {chat?.message}
                </p>
              )
            ) : null}

            {chat?.moreDetails && chat.message === 'tip' && (
              <div className="relative rounded-lg bg-blue-500 p-2">
                <p className="mb-1 text-lg">
                  <span
                    className="cursor-pointer font-semibold tracking-wide text-teal-300"
                    onClick={() =>
                      handleOpenUserInfoModal(chat?.moreDetails?.receiverId)
                    }
                  >
                    @{chat?.moreDetails?.receiverName}
                  </span>{' '}
                  just recieved a TIP
                </p>
                <div className="rounded-lg bg-blue-600 p-1 shadow-lg">
                  <p className="flex items-center gap-2 font-bold">
                    <Image
                      src={coinAc}
                      width={10000}
                      height={10000}
                      className="w-5 max-w-full xl:w-5"
                      alt="Coin"
                    />{' '}
                    <span className="mt-1 flex items-center">
                      {chat?.moreDetails?.amount} AC
                    </span>
                  </p>
                </div>
              </div>
            )}

            {chat?.rainDrop && (
              <div className="rounded-lg rounded-tl-none bg-maastrichtBlue-1000 px-2 py-2">
                <p className="my-1">{chat?.rainDrop?.message}</p>
                <div className="relative overflow-hidden rounded-lg">
                  <Image
                    src={coinsGift}
                    width={10000}
                    height={10000}
                    className="h-auto w-full max-w-full"
                    alt="Chat Image"
                  />
                  <div className="absolute right-3 top-3">
                    <p className="text-white mb-1 text-lg font-bold">
                      Coin Drops
                    </p>
                    {chat?.rainDrop?.status === 'complete' ? (
                      <p
                        onClick={() =>
                          openModal(
                            <RainCompleteModal
                              rainDrop={chat?.rainDrop}
                              chat={chat}
                            />,
                          )
                        }
                        className="text-white cursor-pointer rounded-lg bg-red-500 px-2 py-1 font-bold"
                      >
                        Completed
                      </p>
                    ) : chat?.rainDrop?.userId !== userDetails?.userId &&
                      chat?.rainDrop?.grabbedStatus ? (
                      <p className="text-white rounded-lg bg-green-500 px-2 py-1 font-bold">
                        Grabbed
                      </p>
                    ) : chat?.rainDrop?.userId !== userDetails?.userId ? (
                      <button
                        onClick={() => handleGrab(chat)}
                        className="text-white rounded-lg bg-red-500 px-2 py-1 font-bold"
                      >
                        Grab
                      </button>
                    ) : null}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  },
);
ChatMessage.displayName = 'ChatMessage';

const ChatInput = memo(
  ({
    message,
    gifMessage,
    setGifMessage,
    showSuggestions,
    selectedSuggestion,
    handleKeyDown,
    selectSuggestion,
    handleSendMessage,
    handleInputChange,
    handleGifPickerToggle,
    handleEmojiPickerToggle,
    inputRef,
    suggestionsRef,
    tagSuggestion,
    livePlayersCount,
    section,
    isAuthenticated
  }) => {
    const router = useRouter();

    const handleSetting = useCallback(() => {
      router.push('/user/prefrences');
    }, [router]);

    return (
      <div className="relative bg-oxfordBlue-1000 px-[0.625rem] py-5 shadow-[0px_-4px_10px_var(--richBlack-1000)]">
        {showSuggestions && (
          <ul className="absolute bottom-28 mt-1 max-h-60 w-11/12 overflow-y-auto rounded-md bg-maastrichtBlue-1000 shadow-lg">
            {section === 'PublicChat' &&
              tagSuggestion?.map((suggestion, index) => (
                <button
                  type="button"
                  key={suggestion?.username}
                  ref={(el) => (suggestionsRef.current[index] = el)}
                  className={`block w-full cursor-pointer px-4 py-2 ${
                    index === selectedSuggestion ? 'text-white bg-blue-500' : ''
                  }`}
                  onClick={() => selectSuggestion(index)}
                >
                  {suggestion?.username}
                </button>
              ))}
          </ul>
        )}
        <div className="flex items-center rounded-[0.625rem] bg-cetaceanBlue-1000 px-1">
          {gifMessage && (
            <div className="mb-2 flex items-center">
              <Image
                src={gifMessage}
                width={10000}
                height={10000}
                className="w-[5.875rem] max-w-full rounded-lg"
                alt="Selected GIF"
              />
              <IconButton
                onClick={() => setGifMessage(null)}
                className="ml-2 h-6 w-6 min-w-6"
              >
                <CloseIcon className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
              </IconButton>
            </div>
          )}

          <input
            ref={inputRef}
            className={`hover:bg-cetaceanBlue-400 h-10 w-full resize-none rounded-[0.625rem] bg-cetaceanBlue-1000 px-[0.625rem] py-2 placeholder:text-steelTeal-1000 ${!isAuthenticated ? 'cursor-not-allowed' : ''}`}
            placeholder="Type your message"
            value={message}
            readOnly={!isAuthenticated}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            style={{ opacity: gifMessage ? '0' : '1' }}
          />

          <SendHorizontal
            onClick={handleSendMessage}
            className="h-8 w-8 cursor-pointer rounded-[0.313rem] bg-primary-1000 p-1"
          />
        </div>

        <div className="mt-3.5 flex items-center justify-between gap-4">
          <div className="flex items-center gap-[0.313rem] text-[0.813rem] font-normal leading-none text-white-1000">
            <span className="mb-1 inline-block h-[0.313rem] w-[0.313rem] rounded-full bg-green-1000" />
            <span>Online: {livePlayersCount || '-'}</span>
          </div>

          <div className="flex items-center justify-end gap-[0.313rem]">
            <button
              onClick={handleSetting}
              type="button"
              className="group flex items-end justify-center gap-[0.313rem] px-[0.313rem] py-1 text-xs font-normal leading-none text-steelTeal-1000 transition-all duration-300 hover:text-white-1000"
            >
              <SettingIcon className="h-4 w-4 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
              Setting
            </button>
            <button
              type="button"
              onClick={handleGifPickerToggle}
              className="group flex items-end justify-center gap-[0.313rem] px-[0.313rem] py-1 text-xs font-normal leading-none text-steelTeal-1000 transition-all duration-300 hover:text-white-1000"
            >
              <GIFIcon className="h-4 w-4 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
              GIF
            </button>
            <button
              onClick={handleEmojiPickerToggle}
              type="button"
              className="group flex items-end justify-center gap-[0.313rem] px-[0.313rem] py-1 text-xs font-normal leading-none text-steelTeal-1000 transition-all duration-300 hover:text-white-1000"
            >
              <EmojiIcon className="h-4 w-4 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
              Emoji
            </button>
          </div>
        </div>
      </div>
    );
  },
);
ChatInput.displayName = 'ChatInput';

// Main component
export default function ChatWindow() {
  const [privateChatUserDetails, setPrivateChatUserDetails] = useState(null);

  const { userDetails, isAuthenticated } = useAuthStore();
  const { openChat, setOpenChat } = useGeneralStore();
  const { openUserInfoModal } = useUserInfoStore();
  const { openModal } = useModalStore();
  const { isPrivateChatOpen } = usePrivateChatStore();
  const { isGroupChatOpen } = useGroupChatStore();
  const { grabRainDrop } = useGrabRainDrop();

  const {
    message,
    setMessage,
    gifMessage,
    setGifMessage,
    showEmojiPicker,
    setShowEmojiPicker,
    showGifPicker,
    section,
    setSection,
    showSuggestions,
    selectedSuggestion,
    publicChats,
    isPublicChatsLoading,
    chatContainerRef,
    inputRef,
    suggestionsRef,
    tagSuggestion,
    handleKeyDown,
    selectSuggestion,
    handleSendMessage,
    privateChat,
    recipientUser,
    handleInputChange,
    handleGifPickerToggle,
    handleEmojiPickerToggle,
    livePlayersCount,
    newMessagesCount,
    scrollToBottom,
    handleGifSelect,
    fetchGifs,
    setGrabbedChat,
    updateGrabbedRainChat,
  } = useChatWindow();

  useGroupChatWindow(section === 'GroupChat')

  const { data: groupListData } = useGetGroupListQuery({
    search: '',
    enabled: !!isAuthenticated,
  });

  useEffect(() => {
    if (gifMessage && inputRef.current) {
      inputRef.current.focus();
    }
  }, [gifMessage, inputRef]);

  const handleOpenUserInfoModal = useCallback(
    (userId) => {
      openUserInfoModal(userId);
      openModal(
        <UserInfo setPrivateChatUserDetails={setPrivateChatUserDetails} />,
      );
    },
    [openUserInfoModal, openModal],
  );

  const handleGrab = useCallback(
    (chat) => {
      if (!userDetails?.userId) return;

      grabRainDrop({
        userId: userDetails.userId,
        rainDrop: {
          rainId: chat?.rainDrop?.rainId,
          amountType: chat?.rainDrop?.amountType,
          amount: chat?.rainDrop?.amount,
          playerNo: chat?.rainDrop?.playerNo,
          playerType: chat?.rainDrop?.playerType,
        },
      });

      updateGrabbedRainChat(chat, userDetails.userId);
      setGrabbedChat({ userId: userDetails.userId, chat });
    },
    [grabRainDrop, updateGrabbedRainChat, setGrabbedChat, userDetails],
  );

  const groupTotal = useMemo(() => groupListData?.total || 0, [groupListData]);

  const renderMainContent = useCallback(() => {
    if (isPublicChatsLoading) {
      return (
        <div className="flex h-full w-full items-center justify-center">
          <Lottie
            animationData={CommonLoader}
            loop
            className="max-h-10 min-h-10 w-20"
          />
        </div>
      );
    }

    return (
      <div className="flex h-full w-full flex-col bg-black-1000">
        <ChatHeader section={section} setSection={setSection}  isAuthenticated ={isAuthenticated} openModal= {openModal}/>
        <NavTabs
          section={section}
          setSection={setSection}
          newMessagesCount={newMessagesCount}
          groupTotal={groupTotal}
          isAuthenticated ={isAuthenticated}
        />

        <div className="relative flex min-h-0 shrink grow basis-[0%] flex-col">
          <div
            ref={chatContainerRef}
            className="scrollbar-none flex min-h-0 shrink grow basis-[0%] flex-col overflow-y-auto overflow-x-hidden"
          >
            {section === 'PublicChat' && (
              <div className="flex flex-col gap-[0.625rem] px-[0.625rem]">
                {publicChats?.map((chat) => (
                  <ChatMessage
                    key={chat?.userChatId}
                    chat={chat}
                    handleOpenUserInfoModal={handleOpenUserInfoModal}
                    handleGrab={handleGrab}
                    userDetails={userDetails}
                    openModal={openModal}
                  />
                ))}
              </div>
            )}
            {section === 'Friends' && <Friends />}
            {section === 'PrivateChat' &&
              (isPrivateChatOpen ? (
                <PrivateChat
                  privateChat={privateChat}
                  recipientUser={recipientUser}
                  privateChatUserDetails={privateChatUserDetails}
                />
              ) : (
                <RecentChat />
              ))}
            {section === 'GroupChat' &&
              (isGroupChatOpen ? <GroupChatConversion /> : <GroupChat />)}
          </div>

          {showEmojiPicker && (
            <Picker
              autoFocus={false}
              data={data}
              onEmojiSelect={(emoji) => {
                setShowEmojiPicker(false);
                setMessage(message + emoji.native);
              }}
            />
          )}

          {showGifPicker && (
            <div className="absolute bottom-16 left-0 z-50 w-full">
              <div style={{ height: 300, overflowY: 'scroll' }}>
                <Grid
                  fetchGifs={fetchGifs}
                  width={300}
                  columns={2}
                  gutter={6}
                  onGifClick={handleGifSelect}
                />
              </div>
            </div>
          )}

          {(section === 'PublicChat' || isPrivateChatOpen || isGroupChatOpen) && (
            <ChatInput
              message={message}
              setMessage={setMessage}
              gifMessage={gifMessage}
              setGifMessage={setGifMessage}
              showEmojiPicker={showEmojiPicker}
              setShowEmojiPicker={setShowEmojiPicker}
              showGifPicker={showGifPicker}
              showSuggestions={showSuggestions}
              selectedSuggestion={selectedSuggestion}
              handleKeyDown={handleKeyDown}
              selectSuggestion={selectSuggestion}
              handleSendMessage={handleSendMessage}
              handleInputChange={handleInputChange}
              handleGifPickerToggle={handleGifPickerToggle}
              handleEmojiPickerToggle={handleEmojiPickerToggle}
              inputRef={inputRef}
              suggestionsRef={suggestionsRef}
              tagSuggestion={tagSuggestion}
              livePlayersCount={livePlayersCount}
              section={section}
              isAuthenticated= {isAuthenticated}
            />
          )}

          {newMessagesCount > 0 && section === 'PublicChat' && (
            <button
              type="button"
              onClick={() => scrollToBottom(true)}
              className="absolute bottom-32 right-2 flex items-center gap-2 rounded-xl bg-tiber-1000 p-2"
            >
              <MessageCircleMore className="text-white-1000 transition-all duration-300" />
              New Messages
              <CircleArrowDown className="text-white-1000 transition-all duration-300" />
            </button>
          )}
        </div>
      </div>
    );
  }, [
    isPublicChatsLoading,
    section,
    setSection,
    newMessagesCount,
    groupTotal,
    publicChats,
    handleOpenUserInfoModal,
    handleGrab,
    userDetails,
    openModal,
    privateChat,
    recipientUser,
    privateChatUserDetails,
    isPrivateChatOpen,
    isGroupChatOpen,
    showEmojiPicker,
    showGifPicker,
    message,
    setMessage,
    setShowEmojiPicker,
    fetchGifs,
    handleGifSelect,
    handleKeyDown,
    selectSuggestion,
    handleSendMessage,
    handleInputChange,
    handleGifPickerToggle,
    handleEmojiPickerToggle,
    inputRef,
    suggestionsRef,
    tagSuggestion,
    livePlayersCount,
    showSuggestions,
    selectedSuggestion,
    gifMessage,
    setGifMessage,
    chatContainerRef,
    scrollToBottom,
  ]);

  return (
    <div className="">
      <button
        type="button"
        className={`${openChat ? 'right-[20.5rem]' : 'right-0'} group fixed bottom-[1.125rem] z-[41] hidden h-8 w-8 min-w-8 items-center justify-center rounded-l bg-oxfordBlue-1000 transition-all duration-300 hover:bg-primary-1000 active:scale-90 xl:flex`}
        onClick={() => setOpenChat(!openChat)}
      >
        {openChat ? (
          <ArrowRightIcon className="h-[1rem] w-[1rem] fill-white-1000 transition-all duration-300" />
        ) : (
          <ChatIcon className="h-[1rem] w-[1rem] fill-white-1000 transition-all duration-300" />
        )}
      </button>

      <section
        className={`${openChat ? 'right-0' : 'right-[-100%] xl:right-[-20.5rem]'} blurColorChatWindow fixed top-0 z-40 h-[calc(100dvh_-_7.5rem)] w-full bg-cetaceanBlue-1000 transition-all duration-300 ease-in-out xs:max-w-chatWidth xl:h-dvh`}
      >
        {renderMainContent()}
      </section>
    </div>
  );
}
