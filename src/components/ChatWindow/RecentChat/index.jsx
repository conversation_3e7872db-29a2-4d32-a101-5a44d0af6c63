/* eslint-disable no-nested-ternary */

'use client';

import React, { useEffect, useState } from 'react';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import SearchIcon from '@/assets/icons/Search';
import { useGetRecentChatsQuery } from '@/reactQuery/chatWindowQuery';
import MainLoader from '@/components/Common/Loader/MainLoader';
import CustomImage from '@/components/Common/CustomImage';
import NoDataFound from '@/components/Common/NoDataFound';
import profile from '@/assets/icons/profile-icon.svg';
import useAuthStore from '@/store/useAuthStore';

function RecentChat() {
  const [search, setSearch] = useState('');
  const { isAuthenticated } = useAuthStore((state) => state);
  const {
    data: recentChats,
    isLoading,
    refetch,
  } = useGetRecentChatsQuery({
    params: {
      page: 1,
      limit: 400,
      search: search || '',
    },
    enabled: isAuthenticated, // Query fetches only if authenticated
  });
  const { setIsPrivateChatOpen, setUserId } = usePrivateChatStore(
    (state) => state,
  );
  useEffect(() => {
    if (isAuthenticated) {
      refetch();
    }
  }, [isAuthenticated, refetch]);

  const openChat = (userId) => {
    setUserId(userId);
    setIsPrivateChatOpen(true);
  };

  return (
    <div className="flex flex-col gap-2 p-2">
      <div className="flex items-center gap-2 bg-maastrichtBlue-1000 px-[0.625rem] py-1 shadow-[0px_-4px_10px_var(--richBlack-1000)]">
        <SearchIcon className="h-[1rem] w-[1rem] fill-white-1000 transition-all duration-300" />
        <input
          className="h-10 w-full resize-none rounded-[0.625rem] bg-cetaceanBlue-1000 px-[0.625rem] py-2 placeholder:text-steelTeal-1000"
          placeholder="search"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
        />
      </div>
      {isLoading ? (
        <div className="mt-8 flex w-full items-center justify-center">
          <MainLoader className="w-20" />
        </div>
      ) : recentChats?.length ? (
        recentChats?.map((recentChat) => {
          return (
            <div
              key={`${recentChat?.['recipientUser.receiver_name']}${recentChat?.actionee_id}`}
              className="flex cursor-pointer justify-between gap-[0.625rem] rounded-xl border border-oxfordBlue-1000 bg-maastrichtBlue-1000 p-2"
              onClick={() => openChat(recentChat?.recipient_id)}
              onKeyDown={() => {}}
              role="button"
              tabIndex="0"
            >
              <div className="flex flex-row gap-2.5">
                <div className="relative h-12 w-12 min-w-12 rounded-full border-2 border-oxfordBlue-1000">
                  <CustomImage
                    src={recentChat?.['recipientUser.image_url'] || profile}
                    alt="Profile"
                    width={48}
                    height={48}
                    className="h-full w-full max-w-full rounded-full object-cover object-center"
                    skeletonWidth={48}
                    skeletonHeight={48}
                  />
                  {/* <span className="absolute -bottom-1 -left-1.5 flex h-6 w-6 items-center justify-center rounded-full border-2 border-oxfordBlue-1000 bg-primary-1000 pt-1 text-center text-xs leading-none">
                    14
                  </span> */}
                </div>
                <div className="mt-4 flex justify-center">
                  {recentChat?.['recipientUser.receiver_name']}
                </div>
              </div>
            </div>
          );
        })
      ) : (
        <div className="flex h-full w-full items-center justify-center">
          <NoDataFound className="mt-20 w-28" />
        </div>
      )}
    </div>
  );
}

export default RecentChat;
