'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import VipShoppingCart from '@/assets/icons/Vip-ShoppingCart';
import { useVipTierRules } from '@/hooks/useVipTierRules';
import Skeleton from 'react-loading-skeleton';
import CrystalAndGemImage from '../../assets/images/CrystalsAndGems.png';
import IsolationGem from '../../assets/images/isolationGem.png';
import IsolationArrowGem from '../../assets/images/levelisolationArrow.png';
import VipSlider from './VipSlider';
import ReactTooltip from '../Common/ReactTooltip';

function VipTier() {
  const { isLoading, vipTiers, userWagerAmount } = useVipTierRules();

  const [milestones, setMilestones] = useState([]);

  const [firstAndLast, setFirstAndLast] = useState({});

  const selectedVipTier = vipTiers?.vipTiers?.find(
    (tier) => tier?.userVipTiers?.length > 0,
  );

  const getWagerAmount = selectedVipTier?.wagerAmount;
  // const [nextTier] = vipTiers?.vipTiers?.filter((_, i, arr) => i > 0 && arr[i-1].userVipTiers?.length);
  const nextTier = vipTiers?.vipTiers?.find(
    (_, index, arr) => index > 0 && arr[index - 1].userVipTiers?.length > 0,
  );

  useEffect(() => {
    if (nextTier?.wagerAmount && !Number.isNaN(nextTier?.wagerAmount)) {
      // Calculate 20% of the wager amount
      const twentyPercent = nextTier?.wagerAmount * 0.2;

      // Generate the 5 numbers by multiplying the 20% with different factors
      const generatedNumbers = [
        twentyPercent * 1,
        twentyPercent * 2,
        twentyPercent * 3,
        twentyPercent * 4,
        twentyPercent * 5,
      ];

      const firstNumber = generatedNumbers[0];
      const lastNumber = generatedNumbers[generatedNumbers.length - 1];

      setFirstAndLast({ first: firstNumber, last: lastNumber });
      // Replace the static milestones array with the generated numbers
      const updatedMilestones = generatedNumbers.map((num, index) => {
        return num; // Assuming the static milestones start from 1350
      });

      setMilestones(updatedMilestones);
      console.log(updatedMilestones);
    } else {
      console.log('Invalid wager amount');
    }
  }, [getWagerAmount, nextTier?.wagerAmount]);
  return (
    <div>
      <div className="flex items-baseline gap-4">
        <VipShoppingCart />
        <h4 className="text-2xl font-normal">{selectedVipTier?.name}</h4>
      </div>
      <div className="flex h-dvh max-h-[812px] gap-12 max-lg:flex-wrap tabletView:flex-wrap max-sm:gap-1">
        <div className="flex h-full w-full max-w-[800px] flex-col justify-center gap-[100px] max-md:gap-14 max-sm:mt-8 max-sm:h-fit max-sm:justify-start max-sm:gap-5">
          <div className="relative flex justify-center ">
            <div className="flex aspect-[681/367] h-full max-h-[367px] w-full max-w-[681px] items-center justify-center bg-vip-tier bg-cover bg-no-repeat ">
              <div className="flex  h-full w-full items-center justify-center gap-8 px-11 py-9 max-sm:gap-4">
                <div>
                  <Image
                    src={CrystalAndGemImage}
                    width="204px"
                    height="204px"
                    alt=""
                  />
                </div>

                <div className="flex max-w-[282px] flex-col">
                  {isLoading ? (
                    <Skeleton width={250} height={120} className="block" />
                  ) : (
                    <>
                      <span className="text-4xl font-black leading-8 text-primary-900 max-lg:text-2xl max-lg:leading-5 max-sm:text-xs">
                        {selectedVipTier?.name}
                      </span>
                      <span className="text-4xl font-light leading-8 text-white-1000 max-lg:text-2xl max-lg:leading-5 max-sm:text-xs">
                        {' '}
                        is your current tier
                      </span>
                      <span className="py-4 text-[13px] font-normal leading-3 text-steelTeal-500 max-md:py-2 max-sm:py-1">
                        your progress is an accumulated sum through your wager,
                        increase through tiers to earn bigger rewards
                      </span>
                      <span className="text-xl font-bold  leading-[18px] max-lg:text-base  max-lg:leading-4 max-sm:text-xs">
                        {userWagerAmount}{' '}
                        <span className="text-richBlack-700">
                          /{nextTier?.wagerAmount}
                        </span>
                      </span>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className="mx-auto my-8 w-full max-w-2xl">
            <div className="flex justify-between ">
              {milestones.map((milestone, index) => (
                <div key={index} className="flex flex-col items-center">
                  <div className="mb-2 flex flex-col items-center gap-1">
                    <Image
                      src={IsolationGem}
                      width="200px"
                      height="200px"
                      alt=""
                      className={
                        userWagerAmount >= milestone ? '' : 'grayscale'
                      }
                    />
                    <span
                      className={`text-base font-bold ${userWagerAmount >= milestone ? 'text-white-1000' : 'text-richBlack-900'}`}
                    >
                      {milestone}
                    </span>
                  </div>
                  <span className="">
                    <Image
                      src={IsolationArrowGem}
                      width="12px"
                      height="12px"
                      alt=""
                    />
                  </span>
                </div>
              ))}
            </div>
            <ReactTooltip
              message={userWagerAmount}
              id="my-completed-steps"
              position="bottom-start"
            />
            <div
              id="my-completed-steps"
              className="relative h-2 rounded-full bg-richBlack-600"
            >
              <div
                className="bg-progressBg absolute h-2 rounded-full"
                style={{
                  width: `${((userWagerAmount - firstAndLast?.first) / (firstAndLast.last - firstAndLast?.first)) * 100}%`,
                }}
              />
            </div>
            <div className="mt-[24px] flex w-full items-center justify-center">
              <button
                className="rounded-md bg-primary-900 px-6 py-2 text-center text-xl font-normal"
                type="button"
              >
                Claim Rewards
              </button>
            </div>
          </div>
        </div>
        <div className="">
          <VipSlider vipTierRules={vipTiers?.vipTiers} isLoading={isLoading} />
        </div>
      </div>
    </div>
  );
}

export default VipTier;
