'use client';

import React,{useState, useEffect} from 'react';
import useEmblaCarousel from 'embla-carousel-react';
import Image from 'next/image';
import ArrowVip from '@/assets/icons/Arrow-Vip';
import SliderArrowRectangle from '@/assets/icons/Slider-Arrow-Rectangle';
import Skeleton from 'react-loading-skeleton';
import CrystalAndGemImage from '../../assets/images/CrystalsAndGemsCard.png';
import {
  NextButton,
  PrevButton,
  usePrevNextButtons,
} from '../Common/Button/EmblaArrowButtons';

function VipSlider({ vipTierRules, isLoading }) {
  const [axis, setAxis] = useState('y')
  const [emblaRef, emblaApi] = useEmblaCarousel({
    axis,
    dragFree: true,
    loop: false,
  });
  
 const breakpoints = {
  tabletView: { min: 1280, max: 1520 }, 
 } 
  useEffect(() => {
    const updateAxis = () => {
      if(window.innerWidth < 1000){
        setAxis('x')
      } 
      else if (window.innerWidth >= breakpoints.tabletView.min && window.innerWidth <= breakpoints.tabletView.max) {
        setAxis('x')
      }else {
        setAxis('y')
      }
    }
    updateAxis();

    window.addEventListener('resize', updateAxis);

    return () => {
      window.removeEventListener('resize', updateAxis);
    };
  }, []);

  const {
    prevBtnDisabled,
    nextBtnDisabled,
    onPrevButtonClick,
    onNextButtonClick,
  } = usePrevNextButtons(emblaApi);

  return (
    <div className="relative h-full max-h-[838px]">
      <div
        className="embla relative h-full max-h-[757px] overflow-hidden  px-6"
        ref={emblaRef}
      >
        <div className="embla__container flex h-full flex-col gap-3.5 max-lg:flex-row tabletView:flex-row [&>.embla-slide]:flex-[0_0_calc(100%_/3)] [&>.emblaSlide]:mx-[1%] [&>.emblaSlide]:min-w-0">
          {/* Static Slide Content */}
          {isLoading &&
            [...Array(4)]?.map((item, index) => (
              <div
                key={`skeleton-${index}`}
                className="embla__slide emblaSlide !mx-0"
              >
                <div className="w-full min-w-[298px]">
                  <div className="flex h-full w-full items-center justify-center gap-4 rounded-[45px] px-4 py-9 shadow-vipCardShasow ">
                    <Skeleton width={250} height={120} className="block" />
                  </div>
                </div>
              </div>
            ))}
          {vipTierRules?.map((vip, index) => {
            return (
              <div
                key={`vip-${index}`}
                className="embla__slide emblaSlide  relative !mx-0"
              >
                <div className="relative z-[100] w-full min-w-[298px]">
                  <div
                    className={`flex h-full w-full items-center justify-center gap-4 rounded-[45px] px-4 py-9 ${vip?.userVipTiers?.length > 0 ? 'shadow-vipCardShasow' : 'shadow-vipCardShasowdisable'}`}
                  >
                    <div>
                      <Image
                        src={CrystalAndGemImage}
                        width="80px"
                        height="80px"
                        alt=""
                      />
                    </div>
                    <div className="flex min-w-[133px] max-w-[297px] flex-col gap-4">
                      <span className="text-[28px] font-black leading-8 text-primary-900">
                        {vip?.name}
                      </span>
                      <span className="text-xl font-bold leading-[18px]">
                        {(+vip?.wagerAmount)?.toLocaleString()}{' '}
                        <span className="text-base font-normal text-richBlack-700">
                          Pts
                        </span>
                      </span>
                    </div>
                  </div>
                  {vip?.userVipTiers?.length > 0 && (
                    <PrevButton
                      onClick={onPrevButtonClick}
                      disabled={prevBtnDisabled}
                      className="text-white bg-black absolute left-[-30px] top-1/2 z-[101] -translate-y-1/2 rounded-full p-2 [&>.ArrowCircleLeftIcon]:hidden"
                    >
                      <SliderArrowRectangle />
                    </PrevButton>
                  )}
                </div>

                {/* Only show PrevButton for slides where vip.userVipTiers.length > 0 */}
              </div>
            );
          })}
        </div>
      </div>

      <div className="embla__controls absolute bottom-0 left-0 right-0 z-10 flex -translate-y-1/2 transform justify-between px-4">
        <div className="absolute bottom-0 right-14 flex w-full max-w-[195px] items-center gap-5">
          <span className="text-end text-[13px] font-normal leading-3 text-steelTeal-500">
            SCROLL DOWN FOR MORE RANK
          </span>
          <NextButton
            onClick={onNextButtonClick}
            disabled={nextBtnDisabled}
            className="text-white bg-black rounded-full pr-[5px] [&>.ArrowCircleRightIcon]:hidden"
          >
            <ArrowVip className="h-[50px] w-[50px]" />
          </NextButton>
        </div>
      </div>
    </div>
  );
}

export default VipSlider;
