import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Skeleton from 'react-loading-skeleton';

function CustomImage({
  src,
  alt,
  width,
  height,
  className,
  skeletonWidth,
  skeletonHeight,
}) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    setIsLoading(true);
    setHasError(false);
  }, [src]);

  const handleLoad = () => {
    setIsLoading(false);
  };

  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  return (
    <div style={{ width, height }} className={className}>
      {(isLoading || hasError || !src) && (
        <Skeleton
          width={skeletonWidth || width}
          height={skeletonHeight || height}
          className={`${className} block`}
        />
      )}
      {!hasError && !!src && (
        <Image
          src={src}
          alt={alt}
          width={1000}
          height={1000}
          className={`h-full w-full ${className} ${isLoading ? 'opacity-0' : 'opacity-100'}`}
          onLoad={handleLoad}
          onError={handleError}
          loading="lazy"
        />
      )}
    </div>
  );
}

export default CustomImage;
