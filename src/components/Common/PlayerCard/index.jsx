import React from 'react';
import Image from 'next/image';
import useModalStore from '@/store/useModalStore';
import UserInfo from '@/components/UserInfoModal';
import useUserInfoStore from '@/store/useUserInfoStore';

function PlayerCard({ player, width, height }) {
  const { closeModal, openModal } = useModalStore((state) => state);
  const { openUserInfoModal } = useUserInfoStore((state) => state);
  const handleOpenUserInfoModal = (userId) => {
    openUserInfoModal(userId);
    openModal(<UserInfo />);
  };

  return (
    <div className="sm:col-span-1 max-sm:w-[14%] max-sm:min-w-[14%] max-xs:w-[19%] max-xs:min-w-[19%]">
      <div className="flex flex-col items-center gap-1.5 overflow-hidden">
        <div
          className="aspect-square w-full max-w-16 cursor-pointer overflow-hidden rounded-full"
          onClick={() => handleOpenUserInfoModal(player?.userId)}
        >
          {player.profileImage && (
            <Image
              src={player.profileImage}
              width={width}
              height={height}
              className="w-full max-w-full"
              alt={player.username}
            />
          )}
        </div>
        <h6 className="w-full overflow-hidden text-ellipsis whitespace-nowrap text-center text-[0.625rem] font-normal text-white-1000">
          {player.username}
        </h6>
      </div>
    </div>
  );
}

export default PlayerCard;
