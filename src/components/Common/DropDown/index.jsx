'use client';

import React, { useState, useEffect, useRef } from 'react';
import CaretDownIcon from '@/assets/icons/Caret-Down';

function DropDown({ items }) {
  const [active, setActive] = useState(false);
  const accordionRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        accordionRef.current &&
        !accordionRef.current.contains(event.target)
      ) {
        setActive(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const toggleAccordion = () => {
    setActive((current) => !current);
  };

  return (
    <div className="" ref={accordionRef}>
      <button
        onClick={() => toggleAccordion()}
        type="button"
        className="flex w-full items-center justify-start gap-2.5 rounded-[0.625rem] p-2.5 hover:bg-inputBgColor"
        aria-controls="dropdown-pages"
        data-collapse-toggle="dropdown-pages"
      >
        {items.title}
        <CaretDownIcon
          className={`${active ? 'rotate-180' : ''} ml-auto size-6 fill-white-1000 transition-all duration-300`}
        />
      </button>
      <div
        id="dropdown-pages"
        role="region"
        aria-labelledby="dropdown-pages"
        className={`grid overflow-hidden text-sm text-slate-600 transition-all duration-300 ease-in-out ${active ? 'grid-rows-[1fr] space-y-2 py-2 opacity-100' : 'grid-rows-[0fr] opacity-0'}`}
      >
        <ul className="overflow-hidden">{items.content}</ul>
      </div>
    </div>
  );
}

export default DropDown;
