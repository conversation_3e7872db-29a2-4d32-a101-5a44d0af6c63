'use client';

import useGeneralStore from '@/store/useGeneralStore';

export default function MainSection({ children }) {
  const { openChat } = useGeneralStore((state) => state);
  return (
    <div
      className={`${openChat ? 'xl:mr-[20.5rem] xl:w-[calc(100%-14.75rem-20.5rem)]' : 'xl:mr-[0rem] xl:w-[calc(100%-14.75rem-0rem)]'} blurColor bg-cover bg-no-repeat bg-vip-Container-bg relative z-[2] min-h-dvh overflow-x-hidden px-3 transition-all duration-300 ease-in-out md:px-8 xl:ml-[14.75rem] pt-[calc(var(--header-heigth)+1rem)] lg:pt-[calc(var(--header-heigth)+2.5rem)]`}
    >
      <div className=" mx-auto w-full max-w-containerWidth">{children}</div>
    </div>
  );
}
``