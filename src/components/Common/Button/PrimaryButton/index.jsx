'use client';

import React from 'react';
import <PERSON><PERSON> from 'lottie-react';
import CommonLoader from '../../../../assets/json/button-loader.json';

function PrimaryButton({
  children,
  type = 'button',
  onClick,
  className = '',
  isLoading = false,
  disabled,
  variant = 'primary', // <-- new prop
}) {
  // Define styles based on variant
  const baseClasses =
    'flex h-10 cursor-pointer items-center justify-center rounded-lg px-3 py-1.5 text-base font-normal transition-all duration-300 active:scale-90';

  let variantClasses = '';
  switch (variant) {
    case 'secondary':
      variantClasses =
        'w-full text-cetaceanBlue-1000 bg-TintGoldGradient rounded-lg font-semibold';
      break;
    case 'primary':
    default:
      variantClasses =
        'border-2 border-solid border-primary-1000 bg-primary-1000 text-white-1000';
      break;
  }

  const disabledClasses = disabled
    ? 'text-steelTeal-1000 opacity-80 cursor-not-allowed'
    : '';

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled || isLoading}
      className={`${baseClasses} ${variantClasses} ${disabledClasses} ${className}`}
    >
      {isLoading ? (
        <Lottie animationData={CommonLoader} loop className="h-full w-8" />
      ) : (
        children
      )}
    </button>
  );
}

export default PrimaryButton;
