import React from 'react';

function PrimaryButtonOutline({
  children,
  className,
  ...props
}) {
  return (
    <button
      {...props}
      className={`text-white h-10 rounded-lg border-2 border-solid border-secondaryBtnBg bg-secondaryBtnBg px-3 py-1.5 text-base font-semibold transition-all duration-300 active:scale-90 ${className}`}
    >
      {children}
    </button>
  );
}

export default PrimaryButtonOutline;
