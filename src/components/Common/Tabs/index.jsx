'use client';

import { useState } from 'react';

function Tabs({ tabs, classes, setSelectedTab }) {
  const [activeTab, setActiveTab] = useState(
    Number(localStorage.getItem('activeTab')) || 0,
  );
  const handleTabChange = (index) => {
    localStorage.setItem('activeTab', index);
    setActiveTab(index);
    if (setSelectedTab) setSelectedTab(index);
  };
  return (
    <>
      <div className={`flex justify-center md:justify-start ${classes}`}>
        <ul className="before:opacity-1 relative flex w-full items-center justify-start border-cetaceanBlue-2000 border-b-2">
          {tabs?.map((tab, idx) => (
            <li className="z-[1] w-full " key={tab.label}>
              <button
                type="button"
                aria-current="page"
                onClick={() => handleTabChange(idx)}
                className={`w-full text-base font-normal relative ${activeTab === idx ? 'before:content-[""] before:absolute before:-bottom-px before:left-0 before:h-[2px] before:w-full before:bg-white-1000' : 'text-steelTeal-200'} inline-block rounded-md px-5 py-2.5 text-center`}
              >
                {tab.label}
              </button>
            </li>
          ))}
        </ul>
      </div>
      <div className="tabsContent mx-auto w-full">
        {tabs[activeTab].content}
      </div>
    </>
  );
}

export default Tabs;
