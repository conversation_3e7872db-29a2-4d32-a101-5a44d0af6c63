'use client';

import React, { useEffect, useState, useCallback } from 'react';
import useEmblaCarousel from 'embla-carousel-react';
import Image from 'next/image';
import PromotionImg1 from '../../assets/images/hero-img.jpg';
import PrimaryButtonOutline from '../Common/Button/PrimaryButtonOutline';
import PrimaryButton from '../Common/Button/PrimaryButton';
import GoogleIcon from '@/assets/icons/GoogleIcon';
import FacebookIcon from '@/assets/icons/FacebookIcon';
import DiscordIcon from '@/assets/icons/DiscordIcon';
import TwitchIcon from '@/assets/icons/TwitchIcon';

function PromotionSlider() {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    loop: true,
    slidesToScroll: 1,
    align: 'start',
  });
  const [selectedIndex, setSelectedIndex] = useState(0);

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setSelectedIndex(emblaApi.selectedScrollSnap());
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;
    emblaApi.on('select', onSelect);
    onSelect();
  }, [emblaApi, onSelect]);

  // Autoplay logic
  useEffect(() => {
    if (!emblaApi) return;

    const autoplay = () => {
      if (emblaApi.canScrollNext()) {
        emblaApi.scrollNext();
      } else {
        emblaApi.scrollTo(0);
      }
    };

    const autoplayInterval = setInterval(autoplay, 400000); // Adjust the interval time as needed

    return () => {
      clearInterval(autoplayInterval);
    };
  }, [emblaApi]);

  return (
    <div className="embla mb-9 overflow-hidden" ref={emblaRef}>
      <div className="embla__container [&>.embla-slide]:bg-charcoalBlack-700 flex [&>.embla-slide]:relative [&>.embla-slide]:min-w-0 [&>.embla-slide]:flex-[0_0_100%] [&>.embla-slide]:overflow-hidden [&>.embla-slide]:rounded-2xl">
        <div className="embla__slide embla-slide text-white">
          <div className='overflow-hidden rounded-2xl border border-solid border-heroBannerBorder px-10 py-8 bg-heroBannerBg bg-no-repeat bg-left bg-cover'>
            {/* <Image src={PromotionImg1} className="rounded-sm" /> */}
            <div className=" flex-col justify-center">
              <h2 className="text-white uppercase text-4xl max-xxl:text-[2rem] font-bold leading-[3rem] max-w-[24.5rem] w-full">
                Mic Up With Friends And Start Winning!
              </h2>
              <p className="text-xl max-xxl:text-base leading-none text-white-1000 uppercase font-medium max-w-[24.5rem] w-full mt-0.5">
                Create Groups, Mic Up, And Play
              </p>

              <PrimaryButton variant='secondary' className="mt-2 xxl:mt-5 !px-10 !w-fit">
                Register instantly
              </PrimaryButton>

              <span className="flex items-center py-4 xxl:py-6 max-w-52 w-full">
                <span className="h-px flex-1 bg-white-400"></span>

                <span className="text-sm uppercase font-semibold shrink-0 px-4 text-white-400">OR</span>

                <span className="h-px flex-1 bg-white-400"></span>
              </span>

              <div className="flex items-center justify-between gap-2 max-xxs:justify-center [&>button:hover]:bg-richBlack-500 [&>button]:flex [&>button]:items-center [&>button]:justify-center [&>button]:rounded-md [&>button]:bg-white-400 [&>button]:p-2 [&>button]:transition-all [&>button]:duration-200 [&>button]:ease-in-out max-w-52 w-full">
                <button type="button" className='w-full'>
                  <GoogleIcon />
                </button>
                <button type="button" className='w-full'>
                  <FacebookIcon />
                </button>
                <button type="button" className='w-full'>
                  <DiscordIcon />
                </button>
                <button type="button" className='w-full'>
                  <TwitchIcon />
                </button>

              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default PromotionSlider;
