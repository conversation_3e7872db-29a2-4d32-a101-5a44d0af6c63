'use client';

import { useState, useEffect } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { walletSocket } from '@/utils/socket';
import toast from 'react-hot-toast';
import { BellRing } from 'lucide-react';
import { getAccessToken } from '@/utils/helper';

export function ReactQueryClientProvider({ session, children }) {
  const accessToken = getAccessToken();
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 60 * 1000,
          },
        },
      }),
  );

  function handleNotificationToast(data) {
    toast(data?.data?.message?.message, {
      icon: <BellRing size={20} />,
    });
    queryClient.invalidateQueries(['notificationsDetails']);
  }
  useEffect(() => {
    walletSocket.auth = { token: accessToken };
    walletSocket.connect();
    walletSocket.on('notification', handleNotificationToast);

    return () => {
      walletSocket.off('notification');
    };
  }, []);

  return (
    <QueryClientProvider client={queryClient} contextSharing>
      {children}
    </QueryClientProvider>
  );
}
