'use client';

import React, { useState, useEffect } from 'react';
import FavoriteStrokeIcon from '@/assets/icons/Favorite-Stroke';
import useGameStore from '@/store/useGameStore';
import useAuthStore from '@/store/useAuthStore';
import { useRouter } from 'next/navigation';
import { X } from 'lucide-react'; // Import X icon for clear button

function GamesFilter() {
  const router = useRouter();
  const [localSearchQuery, setLocalSearchQuery] = useState('');
  const { isAuthenticated } = useAuthStore((state) => state);
  const setSearchQuery = useGameStore((state) => state.setSearchQuery);

  useEffect(() => {
    const handler = setTimeout(() => {
      setSearchQuery(localSearchQuery);
    }, 500);

    return () => {
      clearTimeout(handler);
    };
  }, [localSearchQuery, setSearchQuery]);

  const handleSearchInput = (e) => {
    setLocalSearchQuery(e.target.value);
  };

  const handleClearSearch = () => {
    setLocalSearchQuery('');
    setSearchQuery('');
  };

  return (
    <section className="mb-[1.125rem]">
      <div className="flex items-center justify-between gap-[0.375rem] sm:gap-3">
        <div className="flex h-11 w-full max-w-[30.875rem] items-center gap-4 rounded-[0.625rem] bg-maastrichtBlue-1000 px-2 py-1">
          <input
            type="text"
            value={localSearchQuery}
            onChange={handleSearchInput}
            className="h-full w-full bg-transparent text-sm font-normal placeholder:text-white-1000"
            placeholder="Search all games"
          />
          {localSearchQuery && (
            <button
              onClick={handleClearSearch}
              className="flex h-8 w-8 items-center justify-center rounded-full hover:bg-oxfordBlue-1000 transition-all duration-200"
              aria-label="Clear search"
              type="button"
            >
              <X size={16} className="text-steelTeal-1000" />
            </button>
          )}
        </div>

        <div className="flex w-full max-w-[5.875rem] items-center justify-end gap-[0.375rem] sm:max-w-[15.75rem] sm:gap-3">
          {/* <button className="group flex h-11 w-11 items-center justify-center rounded-[0.625rem] bg-oxfordBlue-1000 transition-all duration-300 hover:bg-primary-1000 lgx:hidden uppertab:flex">
            <SortIcon className="m-auto h-4 w-4 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
          </button> */}
          {isAuthenticated && localSearchQuery === '' && (
            <button
              className="group flex h-11 w-11 min-w-11 items-center justify-center rounded-[0.625rem] bg-maastrichtBlue-1000 transition-all duration-300 hover:bg-primary-1000"
              type="button"
              onClick={() => router.push('/favorites')}
            >
              <FavoriteStrokeIcon className="m-auto h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
            </button>
          )}
        </div>
      </div>
    </section>
  );
}

export default GamesFilter;