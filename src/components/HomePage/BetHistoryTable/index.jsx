'use client';

import Tabs from '@/components/Common/Tabs';
import React, { useState } from 'react';
import BetsTable from '@/components/HomePage/BetHistoryTable/BetsTable';
import Select from 'react-select';

function BetHistoryTable() {
  const options = [
    { value: 1, label: 1 },
    { value: 11, label: 11 },
  ];

  const [selectedOption, setSelectedOption] = useState(options[0]);

  const tabs = [
    {
      label: 'Transactions',
      content: <BetsTable betType="myBets" limit={selectedOption} />,
    },
    {
      label: 'All Transactions',
      content: <BetsTable betType="allBets" limit={selectedOption} />,
    },
  ];

  return (
    <section className="mb-10 rounded-lg bg-oxfordBlue-1000 p-4 shadow-container">
      <Select
        className="mobile-input-select ml-auto mt-0 w-16"
        classNamePrefix="mobile-input"
        defaultValue={selectedOption}
        onChange={setSelectedOption}
        options={options}
        placeholder="1"
      />
      <Tabs tabs={tabs} classes="-mt-12" />
    </section>
  );
}

export default BetHistoryTable;
