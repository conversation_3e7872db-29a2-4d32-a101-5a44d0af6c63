'use client';

import useLobby from '@/hooks/useLobby';
import MainLoader from '@/components/Common/Loader/MainLoader';
import useGameStore from '@/store/useGameStore';
import defaultImage from '@/assets/images/demo-image/card-image.png';
import useAuthStore from '@/store/useAuthStore';
import CategoryCarousel from '../CategoryCarousel';

const GAMES_PER_ROW = 5;
const ROWS_PER_SLIDE = 2;
const GAMES_PER_SLIDE = GAMES_PER_ROW * ROWS_PER_SLIDE;

const createGroupedSlides = (games) => {
  if (!Array.isArray(games)) return [];

  return games.reduce((slides, _, index) => {
    if (index % GAMES_PER_SLIDE === 0) {
      const gamesInSlide = games.slice(index, index + GAMES_PER_SLIDE);
      const rows = [];

      for (let j = 0; j < gamesInSlide.length; j += GAMES_PER_ROW) {
        rows.push(gamesInSlide.slice(j, j + GAMES_PER_ROW));
      }

      slides.push(rows);
    }
    return slides;
  }, []);
};

const filterCasinoDataByCategory = (rows) => {
  if (!Array.isArray(rows)) return [];

  return Object.entries(
    rows.reduce((categories, game) => {
      const categoryName = game?.casinoCategory?.name?.EN;
      if (!categoryName) return categories;

      const gameData = {
        id: game?.id,
        name: game?.name,
        iconUrl: game?.iconUrl || defaultImage,
        demoAvailable: game?.demoAvailable,
        uniqueId: game?.uniqueId,
        restrictedCountries: game?.restrictedCountries,
        casinoProviderId: game?.casinoProviderId,
        isFavorite: game?.isFavorite,
      };

      return {
        ...categories,
        [categoryName]: [...(categories[categoryName] || []), gameData],
      };
    }, {}),
  ).map(([categoryName, games]) => ({
    categoryName,
    games,
  }));
};

function NoGamesAvailable() {
  return (
    <section className="mb-10 w-full rounded-lg bg-oxfordBlue-1000 text-center shadow-container">
      No Game Available!
    </section>
  );
}

function LoadingSpinner() {
  return (
    <div className="flex items-center justify-center">
      <MainLoader className="max-h-10 min-h-10" />
    </div>
  );
}

export default function LobbyGames() {
  const searchQuery = useGameStore((state) => state.searchQuery);
  const { userDetails } = useAuthStore((state) => state);
  const { lobbyGames, isLobbyGamesLoading, isSuccess } = useLobby({
    name: searchQuery,
    userId: userDetails?.id,
  });

  if (isLobbyGamesLoading) {
    return <LoadingSpinner />;
  }

  if (!lobbyGames?.count && isSuccess) {
    return <NoGamesAvailable />;
  }

  const filteredData = filterCasinoDataByCategory(lobbyGames?.rows);

  return (
    <>
      {filteredData.map((subCategory) => {
        const { games, categoryName } = subCategory ?? {};

        if (!games?.length) return null;

        const groupedSlides = createGroupedSlides(games);

        return (
          <section
            key={categoryName}
            className="mb-10 rounded-lg bg-oxfordBlue-1000 shadow-container"
          >
            <CategoryCarousel
              title={categoryName}
              groupedSlides={groupedSlides}
            />
          </section>
        );
      })}
    </>
  );
}
