'use client';

import useEmblaCarousel from 'embla-carousel-react';
import GameCard from '@/components/Common/GameCard';
import { useRouter } from 'next/navigation';
import useAuthStore from '@/store/useAuthStore';
import useAuthTab from '@/store/useAuthTab';
import useModalStore from '@/store/useModalStore';
import Auth from '@/components/Auth';
import toast from 'react-hot-toast';
import {
  PrevButton,
  NextButton,
  usePrevNextButtons,
} from '../../Common/Button/EmblaArrowButtons/index';

export default function CategoryCarousel({ title, groupedSlides }) {
  const { isAuthenticated } = useAuthStore((state) => state);
  const { setSelectedTab } = useAuthTab((state) => state);
  const { openModal } = useModalStore((state) => state);
  const options = { slidesToScroll: 'auto' };
  const [emblaRef, emblaApi] = useEmblaCarousel(options);
  const router = useRouter();
  const {
    prevBtnDisabled,
    nextBtnDisabled,
    onPrevButtonClick,
    onNextButtonClick,
  } = usePrevNextButtons(emblaApi);

  const handleGameClick = (gameId) => {
    if (!isAuthenticated) {
      localStorage.setItem('activeTab', 0);
      setSelectedTab(0);
      openModal(<Auth />);
      return;
    }
    router.push(`/game/${gameId}`);
  };

  const handleViewAllClick = (categoryName) => {
    router.push(`/category/${categoryName}`);
  };

  return (
    <div>
      {/* Title and Arrows */}
      <div className="flex items-center justify-between gap-4 px-6 pt-8">
        <h3 className="text-2xl font-bold text-white-1000">{title}</h3>
        {groupedSlides?.length > 1 && (
          <div className="embla__buttons flex items-center gap-2">
            <button
              className="text-base font-semibold text-white-1000 underline transition-all duration-300 ease-in-out hover:scale-90"
              onClick={() => handleViewAllClick(title)}
            >
              View All
            </button>

            <div className="flex items-center gap-1">
              <PrevButton
                onClick={onPrevButtonClick}
                disabled={prevBtnDisabled}
              />
              <NextButton
                onClick={onNextButtonClick}
                disabled={nextBtnDisabled}
              />
            </div>
          </div>
        )}
      </div>

      <div className="embla">
        <div
          className="embla__viewport overflow-hidden px-4 pb-4 pt-7"
          ref={emblaRef}
        >
          <div className="embla__container -mx-3 flex">
            {groupedSlides?.map((rows, slideIndex) => (
              <div
                className="embla__slide min-w-0 shrink-0 grow-0 basis-full px-3"
                key={slideIndex}
              >
                {rows?.map((row, rowIndex) => (
                  <div
                    className="mb-4 grid grid-cols-5 gap-3 max-md:grid-cols-4 max-sm:grid-cols-3"
                    key={rowIndex}
                  >
                    {row?.map((game, index) => (
                      <div key={index + game?.name}>
                        <GameCard
                          src={game?.iconUrl}
                          alt={game?.name}
                          width="10000"
                          height="10000"
                          onClick={() => handleGameClick(game?.id)}
                          aspectRatio="aspect-[222/222]"
                          isFavorite={game?.isFavorite}
                          gameId={game?.id}
                        />
                      </div>
                    ))}
                  </div>
                ))}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
