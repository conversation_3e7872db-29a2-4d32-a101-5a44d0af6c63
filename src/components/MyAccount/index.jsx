'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import Select from 'react-select';
import { toast } from 'react-hot-toast';
import { usePathname, useRouter } from 'next/navigation';
import EditIcon from '../../assets/images/demo-image/edit-icon.svg';
import { useUploadProfileImageMutation } from '@/reactQuery/authQuery';
import { useQueryClient } from '@tanstack/react-query';
import useAuthStore from '@/store/useAuthStore';
import CustomImage from '../Common/CustomImage';
import profile from '@/assets/icons/profile-icon.svg'

const sideLinkOptions = [
  { value: 'Profile', label: 'Profile', href: '/user/profile' },
  { value: 'Email', label: 'Email', href: '/user/email' },
  { value: 'Password', label: 'Password', href: '/user/password' },
  { value: 'Two Factor', label: 'Two Factor', href: '/user/two-factor' },
  { value: 'Preferences', label: 'Preferences', href: '/user/prefrences' },
  { value: 'Ignored User', label: 'Ignored User', href: '/user/ignored-users' },
  { value: 'KYC', label: 'KYC', href: '/user/kyc' },
  // {
  //   value: 'Responsible Gaming',
  //   label: 'Responsible Gaming',
  //   href: '/user/responsible-gaming',
  // },
];

function MyAccount() {
  const router = useRouter();
  const pathname = usePathname();
  const { userDetails } = useAuthStore((state) => state);
  const [selectedOption, setSelectedOption] = useState(
    sideLinkOptions.find((option) => option.href === pathname) ||
      sideLinkOptions[0],
  );
  const [profileImage, setProfileImage] = useState('');
  const queryClient = useQueryClient();

  const { mutate: uploadProfileImage } = useUploadProfileImageMutation({
    onSuccess: (data) => {
      setProfileImage(
        data?.data?.data?.profileImage
          ? `${data?.data?.data?.profileImage}?t=${new Date().getTime()}`
          : '',
      );
      toast.success('Profile image uploaded successfully!');
      queryClient.invalidateQueries(['USER_PROFILE']);
    },
    onError: (error) => {
      const message =
        error.response?.data?.errors?.[0]?.description ||
        'Something went wrong';
      toast.error(message);
    },
  });

  useEffect(() => {
    setProfileImage(
      userDetails?.imageUrl
        ? `${userDetails?.imageUrl}?t=${new Date().getTime()}`
        : '',
    );
  }, [userDetails]);

  const handleRouteChange = (item) => {
    setSelectedOption(item);
    router.push(item.href);
  };

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      const formData = new FormData();
      formData.append('file', file);
      uploadProfileImage(formData);
    }
  };

  return (
    <div className="w-full max-w-profileSidebar rounded-xl bg-richBlack-200 p-[56px_21px] max-xxl:max-w-[200px] max-xxl:p-[56px_8px] max-xl:items-center max-lg:flex max-lg:max-w-[768px] max-lg:justify-evenly max-lg:rounded-[0_40px_0_0] max-lg:py-8 max-xs:justify-between max-xs:gap-6">
      <div className="text-center">
        <div className="relative mx-auto h-[120px] w-[120px] rounded-full rounded-tl-none max-sm:h-[75px] max-sm:w-[75px]">
          <CustomImage
            src={profileImage || profile}
            alt="Profile"
            width={120}
            height={120}
            className="h-[120px] w-[120px] rounded-[20px] max-sm:h-[75px] max-sm:w-[75px]"
            skeletonWidth={120}
            skeletonHeight={120}
          />
          <label
            htmlFor="userImage"
            className="absolute bottom-0 right-0 cursor-pointer"
          >
            <Image
              src={EditIcon}
              width={10000}
              height={10000}
              className="h-[40px] w-[40px] max-sm:h-7 max-sm:w-7"
              alt="Edit"
            />
            <input
              id="userImage"
              type="file"
              className="hidden"
              onChange={handleFileChange}
            />
          </label>
        </div>
        <p className="pt-2 text-[25px] font-normal max-xxl:text-xl">
          {userDetails?.username}
        </p>
      </div>

      <div className="w-1/2 lg:hidden max-xs:w-full">
        <Select
          className="profile-menu-select"
          classNamePrefix="profile-menu"
          placeholder="Select Country"
          defaultValue={selectedOption}
          onChange={handleRouteChange}
          options={sideLinkOptions}
          isSearchable={false}
        />
      </div>

      <div className="px-2.5 pt-[72px] max-xxl:px-2 max-lg:hidden">
        {sideLinkOptions?.map((sideLink) => {
          return (
            <Link
              key={sideLink?.href}
              className="text-primaryColor-500 hover:text-white group relative z-[1] mb-4 inline-block w-full cursor-pointer overflow-hidden rounded-[10px] bg-transparent p-[11px_10px] text-xl capitalize leading-none transition-all duration-500 ease-in-out max-xxl:mb-1 max-xxl:text-base"
              href={sideLink?.href}
            >
              <span className="absolute inset-0 z-[-1] bg-nav-gradient opacity-0 transition-all duration-500 ease-in-out group-hover:opacity-100" />
              {sideLink?.label}
            </Link>
          );
        })}
      </div>
    </div>
  );
}

export default MyAccount;
