'use client';

import React, { useEffect, useState } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';

import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import deliverAddress from '@/schemas/deliverAddress';
import { useCityQuery, useStateQuery } from '@/reactQuery/authQuery';
import useCart from '@/hooks/useCartHook';
import { useUpdateCartAddressMutation } from '@/reactQuery/inventoryQuery';
import { FormInput, FormSelect } from '@/components/Form';

function DeliveryAddressForm({ onNext, params, userMeta }) {
  const [searchQuery, setSearchQuery] = useState('');
  const [orderSessionId, setOrderSessionId] = useState(null);
  const [cityError, setCityError] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm({
    resolver: yupResolver(deliverAddress),
    defaultValues: {
      deliveryAddress1:
        userMeta?.defaultDeliveryAddress?.deliveryAddress1 || '',
      deliveryAddress2:
        userMeta?.defaultDeliveryAddress?.deliveryAddress2 || '',
      city: userMeta?.defaultDeliveryAddress?.city || null,
      state: userMeta?.defaultDeliveryAddress?.state || null,
      postalCode: userMeta?.defaultDeliveryAddress?.postalCode || '',
      makeDefaultAddress: userMeta ? true : false,
    },
  });

  const { data: stateData, isLoading: stateLoading } = useStateQuery({
    countryCode: 'MM',
  });
  const { data: cityData, isLoading: cityLoading } = useCityQuery({
    countryCode: 'MM',
  });
  const { refetch, cartItems } = useCart({ searchQuery });

  const watchCity = watch('city');
  const watchState = watch('state');

  useEffect(() => {
    if (userMeta?.defaultDeliveryAddress) {
      setValue(
        'deliveryAddress1',
        userMeta.defaultDeliveryAddress.deliveryAddress1,
      );
      setValue(
        'deliveryAddress2',
        userMeta.defaultDeliveryAddress.deliveryAddress2,
      );
      setValue('city', userMeta.defaultDeliveryAddress.city);
      setValue('state', userMeta.defaultDeliveryAddress.state);
      setValue('postalCode', userMeta.defaultDeliveryAddress.postalCode);
      setValue('makeDefaultAddress', true);

      const cityExists = cityData?.some(
        (city) =>
          city.inventoryDeliveryChargeId ===
          userMeta.defaultDeliveryAddress.city,
      );
      setCityError(!cityExists);
    }
  }, [userMeta, setValue, cityData]);

  useEffect(() => {
    if (cartItems?.rows) {
      setOrderSessionId(cartItems?.rows[0]?.orderSessionId);
    }
  }, [cartItems]);

  const cityOptions = () => {
    return cityLoading
      ? []
      : cityData?.map((item) => ({
          value: item?.inventoryDeliveryChargeId,
          label: item?.city,
        }));
  };

  const stateOptions = () => {
    return stateLoading
      ? []
      : stateData?.map((item) => ({
          value: item?.state_id,
          label: item?.name,
        }));
  };

  const cityValue = cityOptions().find((item) => item.value === watchCity);
  const stateValue = stateOptions().find((item) => item.value === watchState);

  const { mutate: updateCartAddress, isLoading: isUpdating } =
    useUpdateCartAddressMutation({
      onSuccess: () => onNext(),
      onError: (error) => console.error('Error updating cart address:', error),
    });

  const handleCityChange = (val) => {
    setValue('city', val.value);
    setCityError(false);
  };

  const onSubmit = (formValues) => {
    if (cityError) {
      return;
    }

    const data = {
      ...formValues,
      orderSessionId,
    };
    updateCartAddress(data);
  };
  console.log('cityError', cityError);
  return (
    <div className="m-[0_auto] mt-7 w-full max-w-[864px]">
      <form onSubmit={handleSubmit(onSubmit)}>
        <FormInput
          name="deliveryAddress1"
          label="Address 1"
          control={control}
          placeholder="Address"
          error={errors.deliveryAddress1}
        />
        <FormInput
          name="deliveryAddress2"
          label="Address 2"
          control={control}
          placeholder="Address"
          error={errors.deliveryAddress2}
        />
        <div className="mt-4 grid grid-cols-2 gap-5 max-xs:grid-cols-1">
          <FormSelect
            name="city"
            label="City"
            control={control}
            options={cityOptions()}
            onChange={handleCityChange}
            value={cityValue}
            error={errors.city}
          />
          <FormSelect
            name="state"
            label="State"
            control={control}
            options={stateOptions()}
            onChange={(val) => setValue('state', val.value)}
            value={stateValue}
            error={errors.state}
          />
          <FormInput
            name="postalCode"
            label="Postal Code"
            control={control}
            placeholder="Postal Code"
            error={errors.postalCode}
          />
        </div>
        <div className="mb-4 mt-4">
          <Controller
            name="makeDefaultAddress"
            control={control}
            render={({ field }) => (
              <div className="flex items-center">
                <input
                  {...field}
                  checked={field.value}
                  type="checkbox"
                  className="mr-2"
                />
                <label className="block text-base font-normal capitalize text-steelTeal-1000">
                  Mark as default address
                </label>
              </div>
            )}
          />
        </div>
        {cityError && (
          <p className="mb-5 text-red-500">Default city is not available</p>
        )}
        <div className="mt-7 text-center">
          <PrimaryButton type="submit" disabled={!!cityError}>
            Save Address
          </PrimaryButton>
        </div>
      </form>
    </div>
  );
}

export default DeliveryAddressForm;
