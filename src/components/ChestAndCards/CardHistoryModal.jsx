'use client';
import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { X, ChevronLeft, ChevronRight } from 'lucide-react';
import useModalStore from '@/store/useModalStore';
import useCards from '@/hooks/useCards';

import IconButton from '../Common/Button/IconButton';
import useClaimCardBonus from '@/hooks/useClaimCardBonus';
import { useChestAndCardsHistory } from '@/hooks/useChestAndCardHistory';
import NoDataFound from '../Common/NoDataFound';

function CardHistoryModal() {
  const { closeModal } = useModalStore((state) => state);

  const handleCloseModal = () => {
    closeModal();
  };
  useChestAndCardsHistory();
  const { chestAndCardsHistory } = useCards();
  const { claimBonus } = useClaimCardBonus();

  const [cards, setCards] = useState([]);
  const [activeCard, setActiveCard] = useState(null);
  const [isBonusClaimable, setIsBonusClaimable] = useState(false);

  useEffect(() => {
    if (
      chestAndCardsHistory?.completeWord &&
      chestAndCardsHistory.completeWord.length > 0
    ) {
      const generatedCards = chestAndCardsHistory?.completeWord?.map(
        (card, index) => ({
          id: card?.cardId,
          src: card?.cardImage,
          alt: `Card ${card?.cardImage}`,
          label: `Card ${card?.cardImage}`,
          number:
            chestAndCardsHistory?.collectedLetters?.find(
              (collectedLetter) => collectedLetter?.cardId === card?.cardId,
            )?.quantity || '0',
          isCollected: !!chestAndCardsHistory?.collectedLetters?.find(
            (collectedLetter) => collectedLetter?.cardId === card?.cardId,
          ),
        }),
      );

      setCards(generatedCards);
      setActiveCard(generatedCards[0]);
      console.log('generatedCards', generatedCards);
    }

    if (chestAndCardsHistory) {
      const currentDate = new Date();
      const endDate = new Date(chestAndCardsHistory.endDate);
      const claimStartDate = new Date(endDate);
      claimStartDate.setDate(claimStartDate.getDate() + 1);

      const claimEndDate = new Date(claimStartDate);
      claimEndDate.setDate(claimEndDate.getDate() + 6);

      if (currentDate >= claimStartDate && currentDate <= claimEndDate) {
        setIsBonusClaimable(true);
      } else {
        setIsBonusClaimable(false);
      }
    }
  }, [chestAndCardsHistory]);

  const handlePrev = () => {
    if (!activeCard) return;
    const currentIndex = cards.findIndex((card) => card?.id === activeCard?.id);
    const prevIndex = (currentIndex - 1 + cards.length) % cards.length;
    setActiveCard(cards[prevIndex]);
  };

  const handleNext = () => {
    if (!activeCard) return;
    const currentIndex = cards.findIndex((card) => card?.id === activeCard?.id);
    const nextIndex = (currentIndex + 1) % cards.length;
    setActiveCard(cards[nextIndex]);
  };

  return (
    <div
      tabIndex="-1"
      aria-hidden="true"
      className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto"
    >
      <div className="relative max-h-[90vh] w-full max-w-xl">
        <div className="rounded-lg bg-maastrichtBlue-1000 p-2 shadow-lg">
          <div className="flex items-center justify-between p-2">
            <div className="flex items-center gap-3">
              <h3 className="text-md mt-1 cursor-pointer font-semibold leading-none tracking-wide text-steelTeal-1000 hover:text-steelTeal-1000">
                History
              </h3>
            </div>
            <div className="flex items-center gap-4">
              <IconButton
                onClick={handleCloseModal}
                className="h-6 w-6 min-w-6"
              >
                <X className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
              </IconButton>
            </div>
          </div>
          {activeCard ? (
            <div className="p-4">
              <div className="max-h-[70vh] overflow-y-auto">
                <div className="bg-darkBlue-900 rounded-lg p-4 text-center shadow-lg">
                  <div className="flex items-center justify-between text-lg font-bold">
                    <div className="text-white">
                      Completed Words:{' '}
                      {chestAndCardsHistory?.totalCompleteWords || 0}
                    </div>
                    <div>
                      Bonus Pool:{' '}
                      <span className="text-green-900">
                        {' '}
                        {chestAndCardsHistory?.bonusPool} AC
                      </span>
                    </div>
                  </div>
                  <div className="my-4 flex items-center justify-center">
                    <div className="flex gap-2">
                      {cards.map((card) => (
                        <button
                          key={card.id}
                          className={`relative rounded-lg border-4 p-1 ${activeCard.id === card.id ? 'border-primary-1000' : 'border-transparent'}`}
                          onClick={() => setActiveCard(card)}
                        >
                          {card.number > 0 && (
                            <div className="text-white absolute right-1 top-1 rounded-full bg-primary-1000 px-2 py-1 text-sm font-bold">
                              {card.number}
                            </div>
                          )}
                          {
                            <Image
                              src={card.src}
                              alt={card.alt}
                              width={100}
                              height={150}
                              className={
                                card?.isCollected
                                  ? 'rounded-lg shadow-lg '
                                  : 'rounded-lg shadow-lg brightness-50 filter'
                              }
                            />
                          }
                        </button>
                      ))}
                    </div>
                  </div>
                  <div className="my-4 flex items-center justify-between">
                    <button onClick={handlePrev} className="text-white-1000">
                      <ChevronLeft className="h-10 w-10" />
                    </button>
                    <div className="flex gap-2">
                      <Image
                        src={activeCard.src}
                        alt={activeCard.alt}
                        width={200}
                        height={300}
                      />
                    </div>
                    <button onClick={handleNext} className="text-white-1000">
                      <ChevronRight className="h-10 w-10" />
                    </button>
                  </div>
                  <div className="text-md mt-4 flex justify-between text-white-1000">
                    <div className="mb-1">
                      <span className="mr-1 font-bold">Duration:</span>
                      {chestAndCardsHistory?.startDate
                        ? new Date(
                            chestAndCardsHistory?.startDate,
                          ).toLocaleDateString()
                        : 'N/A'}{' '}
                      -{' '}
                      {chestAndCardsHistory?.endDate
                        ? new Date(
                            chestAndCardsHistory?.endDate,
                          ).toLocaleDateString()
                        : 'N/A'}
                    </div>
                    <div>
                      <span className="mr-1 font-bold">Status:</span>

                      <span className="font-bold text-green-900">
                        {' '}
                        {chestAndCardsHistory.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </div>
                  <div className="mb-1 text-left font-bold">
                    Total Completed Words:
                    <span className="ml-2 text-gray-500">
                      {console.log(
                        'totalCollectedCards',
                        chestAndCardsHistory?.totalCollectedCards[0]
                          ?.totalCards,
                      )}
                      {chestAndCardsHistory?.totalCollectedCards?.length > 0
                        ? chestAndCardsHistory?.totalCollectedCards?.[0]
                            ?.totalCards
                        : 0}
                    </span>
                  </div>
                  <div className="mb-1 text-left font-bold">
                    Single Word Prize:
                    {chestAndCardsHistory?.totalCollectedCards.length > 0 ? (
                      <span className="ml-2 text-gray-500">
                        {` ${chestAndCardsHistory?.bonusPool} AC * (
                    ${chestAndCardsHistory?.totalCompleteWords}/${chestAndCardsHistory?.totalCollectedCards[0]?.totalCards})=${(chestAndCardsHistory?.bonusPool * (chestAndCardsHistory?.totalCompleteWords / chestAndCardsHistory?.totalCollectedCards[0]?.totalCards)).toFixed(2)} SC`}
                      </span>
                    ) : (
                      <span className="ml-2 mt-2 justify-start text-left font-normal text-red-1000">
                        No Cards Collected
                      </span>
                    )}
                  </div>

                  <div className=" text-left font-bold">
                    My Prize:
                    {chestAndCardsHistory?.totalCollectedCards.length > 0 ? (
                      <span className="ml-2  text-green-900">
                        {`${(chestAndCardsHistory?.bonusPool * (chestAndCardsHistory?.totalCompleteWords / chestAndCardsHistory?.totalCollectedCards[0]?.totalCards)).toFixed(2)} * ${chestAndCardsHistory?.totalCompleteWords} Words = ${(chestAndCardsHistory?.bonusPool * (chestAndCardsHistory?.totalCompleteWords / chestAndCardsHistory?.totalCollectedCards[0]?.totalCards) * chestAndCardsHistory?.totalCompleteWords).toFixed(2)}`}
                      </span>
                    ) : (
                      <span className="ml-2 mt-2 justify-start text-left font-normal text-red-1000">
                        No Cards Collected
                      </span>
                    )}
                  </div>
                </div>
              </div>

              <button
                onClick={claimBonus}
                className={`text-white mt-5 w-full rounded-md px-6 py-3 text-lg font-semibold ${
                  isBonusClaimable
                    ? 'bg-red-1000'
                    : 'cursor-not-allowed bg-red-300'
                }`}
                disabled={!isBonusClaimable}
              >
                Claim Bonus
              </button>
            </div>
          ) : (
            <NoDataFound className="w-28" />
          )}
        </div>
      </div>
    </div>
  );
}

export default CardHistoryModal;
