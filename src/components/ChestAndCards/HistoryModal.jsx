'use client';
import React, { useState } from 'react';
import { X } from 'lucide-react';
import useModalStore from '@/store/useModalStore';
import IconButton from '../Common/Button/IconButton';
import TransactionsTable from '../TransactionsModal/TransactionsTable';
import { useChestAndCardsTransactions } from '@/hooks/useChestAndCardsTransactions';

function HistoryModal() {
  const { chestAndCardsTransactions, isLoading } =
    useChestAndCardsTransactions();
  const { closeModal } = useModalStore((state) => state);

  const handleCloseModal = () => {
    closeModal();
  };

  const columns = [
    { key: 'transactionid', value: 'Transaction Id' },
    { key: 'opentime', value: 'Open Time' },
    { key: 'gift', value: 'Gift' },
  ];

  const hasTransactions =
    chestAndCardsTransactions?.transactionDetail?.count > 0;
  const data = hasTransactions
    ? chestAndCardsTransactions?.transactionDetail?.rows.map((transaction) => ({
        transactionid: transaction.chestAndCardTransactionId,
        opentime: new Date(transaction.createdAt).toLocaleDateString(),
        gift: transaction.acAmount
          ? `AC-${transaction.acAmount}`
          : transaction.cardImage || null,
      }))
    : [];

  console.log('data', data);
  return (
    <div
      tabIndex="-1"
      aria-hidden="true"
      className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto"
    >
      <div className="relative max-h-[90vh] w-full max-w-xl">
        <div className="rounded-lg bg-maastrichtBlue-1000 p-2 shadow-lg">
          <div className="flex items-center justify-between p-2">
            <div className="flex items-center gap-3">
              <h3 className="text-md mt-1 cursor-pointer font-semibold leading-none tracking-wide text-steelTeal-1000 hover:text-steelTeal-1000">
                History
              </h3>
            </div>
            <div className="flex items-center gap-4">
              <IconButton
                onClick={handleCloseModal}
                className="h-6 w-6 min-w-6"
              >
                <X className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
              </IconButton>
            </div>
          </div>
          <div className="p-4">
            <div className="max-h-[70vh] overflow-y-auto">
              <TransactionsTable
                data={data}
                columns={columns}
                isLoading={isLoading}
                hasTransactions={hasTransactions}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default HistoryModal;
