import React, { useRef, useEffect } from 'react';
import useModalStore from '@/store/useModalStore';
import NotificationIcon from '@/assets/icons/Notification';
import useAuthStore from '@/store/useAuthStore';
import useNotificationStore from '@/store/useNoticeStore';
import { formatDateTime } from '@/utils/helper';
import useChatWindow from '@/hooks/useChatWindow';
import { useUpdateNotificationMutation } from '@/reactQuery/generalQueries';
import { useQueryClient } from '@tanstack/react-query';
import Auth from '../Auth';
import IconButton from '../Common/Button/IconButton';
import ReactTooltip from '../Common/ReactTooltip';
import useNotifications from '@/hooks/useNotification';

function NotificationPopup() {
  // const [showNotificationPopup, setShowNotificationPopup] = useState(!false);
  const { openModal } = useModalStore((state) => state);
  const queryClient = useQueryClient();
  const { isAuthenticated } = useAuthStore();
  // const {setSection}=useChatWindow()
  useNotifications();
  const { setSection } = useChatWindow();
  const popupRef = useRef(null);
  const {
    notifications,
    setShowNotificationPopup,
    showNotificationPopup,
    setPage,
  } = useNotificationStore();
  const updateNotification = useUpdateNotificationMutation({
    onSuccess: (response) => {
      if (response?.data?.success) setPage(1);

      queryClient.invalidateQueries(['notificationsDetails']);
    },
    onError: (error) => {
      const message =
        error.response?.data?.errors?.[0]?.description ||
        'Something went wrong';
    },
  });
  // Close popup when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (popupRef.current && !popupRef.current.contains(event.target)) {
        setShowNotificationPopup(false);
      }
    };

    if (showNotificationPopup) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showNotificationPopup]);
  const handleNotificationClick = (data) => {
    const { referenceType } = data;
    if (referenceType === 'FRIEND_REQUEST') {
      setSection('Friends');
    } else if (
      referenceType === 'GROUP_CHAT' ||
      referenceType === 'GROUP_JOIN'
    ) {
      setSection('GroupChat');
    } else if (referenceType === 'PRIVATE_CHAT') {
      setSection('PrivateChat');
    }
    setShowNotificationPopup(false);
    updateNotification.mutate({ notificationIds: [data?.notificationId] });
  };
  return (
    <div ref={popupRef}>
      <ReactTooltip
        message="Notification"
        id="notification-tooltip"
        position="left"
      />
      <IconButton
        id="notification-tooltip"
        onClick={() => {
          if (!isAuthenticated) {
            localStorage.setItem('activeTab', 0);
            openModal(<Auth />);
            return;
          }
          setShowNotificationPopup(!showNotificationPopup);
        }}
      >
        <div className="relative">
          {/* Notification Icon */}
          <NotificationIcon className="fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />

          {/* Badge (Notification Count) */}
          {notifications?.length > 0 && (
            <span className="text-white absolute -right-1 -top-1 flex h-4 w-4 items-center justify-center rounded-full bg-red-600 text-[10px] font-bold">
              {notifications?.length > 9 ? '9+' : notifications?.length}
            </span>
          )}
        </div>
      </IconButton>

      {showNotificationPopup && (
        <div className="absolute left-0 top-[94px] z-10 grid max-h-[50dvh] w-full gap-2 overflow-y-auto rounded-md border border-[grey] bg-[#000000fa] p-[20px] xl:top-[46px]">
          {notifications.map((data, index) => (
            <button
              type='button'
              key={index}
              className="cursor-pointer rounded bg-[#80808024] p-2"
              onClick={() => handleNotificationClick(data)}
            >
              <p>{data?.message}</p>
              <p>{formatDateTime(data?.createdAt)}</p>
            </button>
          ))}
          {notifications?.length === 0 && (
            <div
              key="notfound"
              className="cursor-pointer rounded bg-[#80808024] p-2"
            >
              <p>No notifications found</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default NotificationPopup;
