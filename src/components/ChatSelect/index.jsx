import React, { useState } from 'react';
import Select from 'react-select';
import Image from 'next/image';
import { useGetFriendsListQuery } from '@/reactQuery/chatWindowQuery';
import UserImg from '../../../public/assets/demo-image/group-img.jpg';

// Custom ValueContainer component
const CustomValueContainer = ({ children, ...props }) => {
  const [values, input] = children || [[], null];

  return (
    <div className='chatMulti-inner-select__group w-full bg-richBlack-1000 rounded-md'>
      <div className='relative basis-full w-full bg-black-1000 px-9 mx-0 mt-0 border border-solid border-borderColor-100 h-[42px] rounded-md before:content-[""] before:absolute before:top-1/2 before:-translate-y-1/2 before:left-3 before:bg-chatSearchIcon before:rounded-md before:z-[1] before:bg-no-repeat before:bg-center before:w-5 before:h-5 mb-0 chatMulti-inner-select__input-container-section'>
        {input}
      </div>
      {values && values.length > 0 && (
        <div className={`flex flex-wrap gap-x-2 gap-y-1 p-4 chatMulti-inner-select__multiOption`}>
          {values}
        </div>
      )}
    </div>
  );
};

// Define the main component
function MultiSelect({ className = '', classNamePrefix = '', onChange }) {
  const { data: friendsList } = useGetFriendsListQuery({
    enabled: true,
    params: { search: '' },
  });

  const [selectedValues, setSelectedValues] = useState([]);

  const handleChange = (selectedOptions) => {
    // Extract only the `value` (user IDs) from the selected options
    const selectedUserIds = selectedOptions
      ? selectedOptions.map((option) => option.value)
      : [];
    setSelectedValues(selectedOptions);
    onChange(selectedUserIds); // Pass the selected user IDs back to parent component
  };

  const dynamicOptions =
    friendsList && friendsList.rows
      ? friendsList.rows.map((friend) => ({
          value: friend.relationUserId,
          label: (
            <div className="flex items-center gap-1 ">
              <Image
                src={friend.relationUser.profileImage || UserImg}
                alt={friend.relationUser.firstName}
                className="h-4 w-4 rounded-full"
                width="100"
                height="100"
              />
              <h6 className="pt-[3px] text-sm leading-none text-white-1000">
                {friend.relationUser.firstName} {friend.relationUser.lastName}
              </h6>
            </div>
          ),
        }))
      : [];

  return (
    <Select
      closeMenuOnSelect={false}
      isMulti
      options={dynamicOptions}
      className={`${className}`}
      classNamePrefix={`${classNamePrefix}`}
      defaultMenuIsOpen={false}
      components={{
        ValueContainer: CustomValueContainer,
      }}
      value={selectedValues}
      onChange={handleChange}
    />
  );
}

export default MultiSelect;
