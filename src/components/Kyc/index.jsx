'use client';

import { createVeriffFrame } from '@veriff/incontext-sdk';
import { useCallback, useEffect, useRef } from 'react';

import useAuthStore from '@/store/useAuthStore';
import veriffyQuery from '@/reactQuery/veriffyQuery';
import InfoModal from './components/InfoModal';

function Kyc() {
  const { setUserDetails } = useAuthStore();
  const veriffFrameRef = useRef(null);

  const handleVerificationSuccess = useCallback(() => {
    setUserDetails((prevDetails) => ({
      ...prevDetails,
      veriffStatus: 'REQUESTED',
    }));
  }, [setUserDetails]);

  const handleVeriffEvent = useCallback(
    (msg, closeFrame) => {
      if (msg === 'CANCELED' || msg === 'FINISHED') {
        closeFrame();
        if (msg === 'FINISHED') {
          handleVerificationSuccess();
        }
      }
    },
    [handleVerificationSuccess],
  );

  const { data, refetch, isSuccess, isLoading } = veriffyQuery.initKYCQuery({
    enabled: false,
    onError: (errData) => {
      console.error('KYC verification error:', errData?.errorCode);
    },
  });

  useEffect(() => {
    if (isSuccess && data?.success && data?.verification?.url) {
      console.log('Creating Veriff frame with URL:', data.verification.url);

      if (veriffFrameRef.current) {
        veriffFrameRef.current.close();
      }

      const veriffFrame = createVeriffFrame({
        url: data.verification.url,
        onEvent: (msg) =>
          handleVeriffEvent(msg, () => {
            if (veriffFrame && typeof veriffFrame.close === 'function') {
              veriffFrame.close();
            }
          }),
      });

      veriffFrameRef.current = veriffFrame;
    }
  }, [data, isSuccess, handleVeriffEvent]);

  const handleVeriffKYC = () => {
    refetch()
      .then((result) => console.log('KYC query completed:', result.isSuccess))
      .catch((err) => console.error('KYC query error:', err));
  };

  return (
    <InfoModal
      title="KYC Verification"
      textTitle="Complete Your KYC Verification"
      textContent="Kindly upload the required documents to initiate the process. Once approved from our support team, your KYC will be verified."
      handleOnClick={handleVeriffKYC}
      buttonLabel={isLoading ? 'Verifying...' : 'Start Verification'}
      disabled={isLoading}
    />
  );
}

export default Kyc;
