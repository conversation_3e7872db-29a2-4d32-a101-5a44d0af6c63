import PropTypes from 'prop-types';
import React from 'react';
import { X } from 'lucide-react';
import IconButton from '@/components/Common/Button/IconButton';

function InfoModal({
  title,
  textTitle,
  textContent,
  handleClose,
  handleOnClick,
  buttonLabel,
  disabled,
}) {
  return (
    <div className="bg-white mx-auto max-w-md rounded-lg p-6 shadow-lg">
      {/* Header */}
      {title !== 'KYC Verification' && (
        <div className="flex items-center justify-between border-b pb-3">
          <h2 className="text-lg font-semibold">{title}</h2>
          <IconButton onClick={handleClose} className="h-6 w-6">
            <X className="hover:fill-white h-5 w-5 fill-steelTeal-1000 transition-all duration-300" />
          </IconButton>
        </div>
      )}

      {/* Content */}
      <div className="mt-4">
        <h3 className="text-md font-medium">{textTitle}</h3>
        <p className="mt-2 text-gray-600">{textContent}</p>
      </div>

      {/* Button */}
      <div className="mt-6 flex justify-end">
        <button
          type="button"
          disabled={disabled}
          onClick={handleOnClick}
          className={`rounded-md bg-gray-200 px-4 py-2 text-gray-800 transition hover:bg-gray-300 ${disabled ? `cursor-progress` : ''}`}
        >
          {buttonLabel}
        </button>
      </div>
    </div>
  );
}

InfoModal.propTypes = {
  title: PropTypes.string.isRequired,
  textTitle: PropTypes.string.isRequired,
  textContent: PropTypes.string.isRequired,
  handleClose: PropTypes.func.isRequired,
  handleOnClick: PropTypes.func.isRequired,
  buttonLabel: PropTypes.string.isRequired,
};

export default InfoModal;
