'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import FavoriteIcon from '@/assets/icons/Favorite';
import ProfileIcon from '@/assets/icons/Profile';
import MyAccountIcon from '@/assets/icons/MyAccount';
import SettingIcon from '@/assets/icons/Setting';
// import NotificationIcon from '@/assets/icons/Notification';
import VIPIcon from '@/assets/icons/VIP';
// import FAQIcon from '@/assets/icons/FAQ';
import useGeneralStore from '@/store/useGeneralStore';
import useModalStore from '@/store/useModalStore';
import { FileBarChart, Receipt } from 'lucide-react';
import Select from 'react-select';
import { usePathname } from 'next/navigation';
import useAuthStore from '@/store/useAuthStore';
import LogoutButton from '../LogoutButton';
import BrandLogo from '../../assets/images/logo/brand-logo.svg';
import DropDown from '../Common/DropDown';
import SpinWheel from '../SpinWheel';
// import Notice from '../Notice';
import DollarIcon from '../../assets/webp/dollar-icon.webp';
import FeatureTabs from '../FeatureTabs';
import HamburgerIcon from '@/assets/icons/Hamburger';
import SearchIcon from '@/assets/icons/SearchIcon';
import FavoriteStrokeIcon from '@/assets/icons/Favorite-Stroke';
import RecentIcon from '@/assets/icons/RecentIcon';
import ChallengesIcon from '@/assets/icons/ChallengesIcon';

const options = [
  { value: 'USD', label: 'USD' },
  // { value: 'INR', label: 'INR' },
  // { value: 'EUR', label: 'EUR' },
];

function SideMenu() {
  const pathname = usePathname();
  const isGame = pathname.includes('/game');

  const [selectedOption, setSelectedOption] = useState(null);
  const { openModal } = useModalStore((state) => state);
  const { userWallet, userDetails, isAuthenticated } = useAuthStore(
    (state) => state,
  );
  // const wallet = userDetails?.wallets?.find(wallet => wallet?.currency?.name === "USD");
  const profileMenuList = [
    {
      component: MyAccountIcon,
      href: '/user',
      text: 'My Account',
    },
    {
      component: SettingIcon,
      href: '/setting',
      text: 'Settings',
    },
    // {
    //   component: NotificationIcon,
    //   text: 'Notice',
    //   button: true,
    //   onClick: () => openModal(<Notice />),
    // },
    {
      component: VIPIcon,
      text: 'VIP',
      href: '/vip',
      // button: true,
      // onClick: () => openModal(<Vip />),
    },
    {
      component: FileBarChart,
      text: 'Game History',
      // button: true,
      // onClick: () => openModal(<TransactionsModal />),
      href: '/transactions',
    },
    {
      component: Receipt,
      href: '/user-transactions',
      text: 'Transactions',
    },
    // {
    //   component: ShareIcon,
    //   href: '/share',
    //   text: 'Share',
    // },
  ];

  const menuList = [
    {
      component: FavoriteIcon,
      href: '/favorites',
      text: 'Favorites',
    },
    // {
    //   component: TaskListIcon,
    //   href: '/task-list',
    //   text: 'Task List',
    // },
    // {
    //   component: FaucetIcon,
    //   text: 'Faucet',
    //   button: true,
    //   onClick: () => openModal(<Faucet />),
    // },
    // {
    //   component: ChestCardIcon,
    //   text: 'Chest and Card',
    //   button: true,
    //   onClick: () => openModal(<ChestAndCardModal />),
    // },

    // {
    //   component: FAQIcon,
    //   href: '/faq',
    //   text: 'FAQ',
    // },
    // {
    //   component: FAQIcon,
    //   href: '/inventory',
    //   text: 'Inventory',
    // },
  ];

  // const originalsMenuList = [
  //   {
  //     component: CrashIcon,
  //     href: '/game/1',
  //     text: 'Crash',
  //   },
  //   {
  //     component: MinesIcon,
  //     href: '/games/3',
  //     text: 'Mines',
  //   },
  //   {
  //     component: PlinkoIcon,
  //     href: '/games/2',
  //     text: 'Plinko',
  //   },
  //   {
  //     component: HiLoIcon,
  //     href: '/games/4',
  //     text: 'Hi-Lo',
  //   },
  //   {
  //     component: CoinFlipIcon,
  //     href: '/games/6',
  //     text: 'Coinflip',
  //   },
  //   {
  //     component: TowerIcon,
  //     href: '/games/5',
  //     text: 'Tower',
  //   },
  // ];

  const { openMenu } = useGeneralStore((state) => state);

  const openSpinWheel = () => {
    openModal(<SpinWheel check />);
  };

  return (
    <aside
      id="default-sidebar"
      className={`${openMenu ? 'right-0 xl:left-0' : 'right-[-100%]'} fixed top-[3.75rem] z-40 h-[calc(100dvh_-_7.5rem)] max-w-sidebarWidth w-full bg-sidebarBgColor transition-all duration-300 ease-in-out xl:left-0 xl:top-0 xl:h-dvh`}
      aria-label="Sidebar"
    >
      <div className='flex items-center justify-between gap-6 px-4 mt-4'>
        <FeatureTabs />
        <HamburgerIcon className='size-6 cursor-pointer' />
      </div>

      <div className='px-4 pt-7 pb-4'>
        <div className='relative bg-inputBgColor h-10 rounded-px_10 overflow-hidden'>
          <SearchIcon className='size-6 absolute left-2 top-1/2 -translate-y-1/2' />
          <input type="search" className='bg-transparent h-full w-full pl-8 pr-2 text-sm font-semibold text-white-1000 placeholder:text-placeholderColor' placeholder='Search' />

        </div>
      </div>
      {/* <div className="mb-8 hidden items-center justify-center pt-10 xl:flex">
        <Link href="/">
          <Image
            src={BrandLogo.src}
            width={1000}
            height={1000}
            className="w-full max-w-[8.75rem]"
            alt="Brand Logo"
          />
        </Link>
      </div> */}

      {/* {isAuthenticated && (
        <div className="mb-2.5 rounded-lg bg-maastrichtBlue-1000 p-2.5">
          <div className="relative flex items-center gap-1">
            {!isGame && (
              <div className="w-full">
                <h2 className='text-base text-white-1000 pb-1.5'>{userDetails?.firstName || ''}</h2>
                <div className="flex w-full items-center justify-between gap-2.5 rounded-lg bg-walletBg p-1">
                  <div className="flex items-center gap-2">
                    <Image
                      src={DollarIcon}
                      width={10000}
                      height={10000}
                      className="h-[22px] w-[22px]"
                      alt="Coin"
                    />
                    <span className="text-base text-white-1000">
                      <span>$</span>
                      {userWallet?.amount || 0}
                    </span>
                  </div>
                  <Select
                    defaultValue={selectedOption}
                    onChange={setSelectedOption}
                    options={options}
                    className="currency-select"
                    classNamePrefix="currency-select-inner"
                    placeholder="USD"
                    isSearchable={false}
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      )} */}

      {/* <div className="flex items-center justify-center py-10">
        <div className="relative flex h-16 w-full cursor-pointer items-center justify-end rounded-lg bg-maastrichtBlue-1000 bg-snipWheelBackground">
          <Lottie
            animationData={SpinWheelLottie}
            loop
            className="absolute -top-5 left-5 w-24"
            onClick={openSpinWheel}
          />
          <div className="mr-5 w-16 text-2xl font-bold leading-6">
            Spin Wheel
          </div>
        </div>
      </div> */}

      <div className="scrollbar-none h-full space-y-2 divide-y divide-white-200 overflow-y-auto overflow-x-hidden xl:h-[calc(100%_-_8rem)]">
        <div className='px-2'>
          <Link href='#' className="flex items-center justify-start gap-2 rounded-md p-2 hover:bg-inputBgColor capitalize" >
            <FavoriteIcon className="size-6 fill-steelTeal-1000" />
            <span className="text-base font-semibold text-steelTeal-200">
              favorite
            </span>
          </Link>
          <Link href='#' className="flex items-center justify-start gap-2 rounded-md p-2 hover:bg-inputBgColor capitalize" >
            <RecentIcon className="size-6 fill-steelTeal-1000" />
            <span className="text-base font-semibold text-steelTeal-200">
              Recent
            </span>
          </Link>
          <Link href='#' className="flex items-center justify-start gap-2 rounded-md p-2 hover:bg-inputBgColor capitalize" >
            <ChallengesIcon className="size-6 fill-steelTeal-1000" />
            <span className="text-base font-semibold text-steelTeal-200">
              favorite
            </span>
          </Link>
        </div>

        {!isAuthenticated && (
          <div className="px-2 pt-2">
            <DropDown
              items={{
                title: (
                  <div className="flex items-center justify-start gap-2.5">
                    <ProfileIcon className="size-6 fill-steelTeal-200" />
                    <span className="text-base font-semibold text-steelTeal-200">
                      Profile
                    </span>
                  </div>
                ),
                content: (
                  <>
                    <DropDownContent contentList={profileMenuList} />
                    <LogoutButton />
                  </>
                ),
              }}
            />
          </div>
        )}

        <div className='px-2 pt-2'>
          <h4 className='text-white-400 text-xs font-semibold uppercase px-2 pb-2'>ENJOY EXCLUSIVE PERKS</h4>
          <Link href='#' className="flex items-center justify-start gap-2 rounded-md p-2 hover:bg-inputBgColor capitalize" >
            <VIPIcon className="size-6 fill-steelTeal-1000" />
            <span className="text-base font-semibold text-steelTeal-200">
            VIP Club
            </span>
          </Link>
        </div>

        {/* <div className="mt-2.5 rounded-lg bg-maastrichtBlue-1000 px-1.5 py-1.5">
          <DropDown
            items={{
              title: (
                <div className="flex items-center justify-start gap-2.5">
                  <OriginalsIcon className="size-6 fill-white-1000" />
                  <span className="text-base font-semibold text-steelTeal-200">
                    Originals
                  </span>
                </div>
              ),
              content: <DropDownContent contentList={originalsMenuList} />,
            }}
          />
        </div> */}

        {!isAuthenticated && (
          <div className="px-2 pt-2">
            <ul className="">
              <li>
                <DropDownContent contentList={menuList} />
              </li>
            </ul>
          </div>
        )}


      </div>
    </aside>
  );
}

export default SideMenu;

function DropDownContent({ contentList }) {
  return contentList.map((item) => {
    const IconComponent = item?.component;
    if (item?.button) {
      return (
        <div
          role="button"
          key={item.text}
          onClick={item.onClick}
          tabIndex={0}
          onKeyDown={() => { }}
          className="flex w-full items-center justify-start gap-2.5 rounded-md p-2.5 hover:bg-inputBgColor"
        >
          <IconComponent className="size-6 fill-steelTeal-1000" />
          <span className="text-base font-semibold text-steelTeal-200">
            {item.text}
          </span>
        </div>
      );
    }
    return (
      <>
        <Link
          key={item.href}
          href={item.href}
          className="flex items-center justify-start gap-2.5 rounded-md p-2.5 hover:bg-inputBgColor"
        >
          <IconComponent className="size-6 fill-steelTeal-1000" />
          <span className="text-base font-semibold text-steelTeal-200">
            {item.text}
          </span>
        </Link>

        <Link
          key={item.href}
          href={item.href}
          className="flex items-center justify-start gap-2.5 rounded-md p-2.5 hover:bg-inputBgColor"
        >
          <IconComponent className="size-6 fill-steelTeal-1000" />
          <span className="text-base font-semibold text-steelTeal-200">
            {item.text}
          </span>
        </Link>
      </>
    );
  });
}
