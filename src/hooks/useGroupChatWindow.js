import { useEffect } from 'react';
import useGroupChatStore from '@/store/useGroupChatStore';
import { getAccessToken } from '@/utils/helper';
import { io } from 'socket.io-client';
import usePrivateChatStore from '@/store/usePrivateChatStore';

const useGroupChatWindow = (isGroupChat) => {

    const {
        appendGroupChat,
        groupName,
      } = useGroupChatStore((state) => state);
      const {
        appendPrivateChat,
        isPrivateChatOpen,
        userId,
      } = usePrivateChatStore((state) => state);

const accessToken = getAccessToken();
      useEffect(() => {
          const handleNewMessage = (newMessage) => {
            appendGroupChat(newMessage?.data);
          };
          
          let socket=null;
          if (isGroupChat) {
          socket = io(`${process.env.NEXT_PUBLIC_SOCKET_URL}/group`, {
              query: { groupName },
              transports: ['websocket'],
              withCredentials: true,
              path: '/api/socket',
              auth: {
                  token: accessToken,
                },
            });
            if(!socket?.connected) socket.connect()
            socket.on('GROUP_CHAT', handleNewMessage);
          }
          return () => {
            if (socket) {
              socket.off('GROUP_CHAT', handleNewMessage);
              socket.disconnect();
            }
          };
        }, [groupName, accessToken, isGroupChat]);

        useEffect(() => {
            const handleNewMessage = (newMessage) => {
              appendPrivateChat(newMessage);
            };
            let socket;
            if (isPrivateChatOpen) {
              socket = io(`${process.env.NEXT_PUBLIC_SOCKET_URL}/private`, {
                query: { userId },
                path: '/api/socket',
                transports: ['websocket'],
                withCredentials: true,
              });
              socket.on('private_chat', handleNewMessage);
            }
            return () => {
              if (socket) {
                socket.off('private_chat', handleNewMessage);
                socket.disconnect();
              }
            };
          }, [isPrivateChatOpen]);

}

export default useGroupChatWindow