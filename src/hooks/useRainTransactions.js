import { useGetRainTransactions } from '@/reactQuery/generalQueries';
import useAuthStore from '@/store/useAuthStore';

export const useRainTransactions = (rainId = null) => {
  console.log('useRainTransactions', rainId);
  const { userDetails } = useAuthStore((state) => state);
  const {
    data: rainTransactions,
    isLoading,
    isError,
    error,
  } = useGetRainTransactions({ rainId, userId: userDetails.userId });

  return {
    rainTransactions,
    isLoading,
    isError,
    error,
  };
};
