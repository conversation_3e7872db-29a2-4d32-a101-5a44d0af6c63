import { useGamesListQuery } from '@/reactQuery/gamesQuery';
import useAuthStore from '@/store/useAuthStore';


function useCategory({ limit, categoryName , pageNo ,name}) {
  const { isAuthenticated } = useAuthStore((state) => state);
  let queryResult = {};
  queryResult = useGamesListQuery({
    enabled: isAuthenticated,
    limit,
    categoryName,
    pageNo, 
    name
  })
  const { data, isLoading: gamesLoading, refetch, isFetching, isFetchingNextPage, hasNextPage, fetchNextPage } = queryResult;
  return {
    casinoGames: data,
    gamesLoading,
    refetch,
    isFetching,
    isFetchingNextPage,
    hasNextPage,
    fetchNextPage,
  }
}

export default useCategory