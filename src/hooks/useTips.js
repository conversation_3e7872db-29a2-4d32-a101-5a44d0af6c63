import { useSendTipMutation } from '@/reactQuery/generalQueries';
import { toast } from 'react-hot-toast';

export const useTips = () => {
  const {
    mutate: sendTip,
    isLoading,
    isError,
    isSuccess,
    data,
    error,
  } = useSendTipMutation({
    onError: (error) => {
      console.log('errrr', error);
      const message =
        error.response?.data?.errors?.[0]?.description || 'Unable to send';
      toast.error(message);
    },
  });

  return {
    sendTip,
    isLoading,
    isError,
    isSuccess,
    data,
    error,
  };
};
