/* eslint-disable import/prefer-default-export */
import { useAllTransactions } from '@/reactQuery/generalQueries';

export const useUserTransactions = ({
  startDate,
  endDate,
  page,
  limit,
  purpose,
}) => {
  const {
    data: userTransactions,
    isLoading,
    isError,
    error,
  } = useAllTransactions({ startDate, endDate, page, limit, purpose });

  return {
    userTransactions,
    isLoading,
    isError,
    error,
  };
};
