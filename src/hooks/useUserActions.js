import { useUserActionMutation } from '@/reactQuery/generalQueries';

const useUserActions = ({ refetch }) => {
  const {
    mutate: performUserAction,
    isLoading,
    isError,
    isSuccess,
    data,
    error,
  } = useUserActionMutation({
    onSuccess: (response) => {
      console.log('User action performed successfully:', response);
      refetch();
    },
    onError: (err) => {
      console.error('Error performing user action:', err);
    },
  });

  const ignoreUser = (relationUserId) => {
    performUserAction({ relationUserId, ignoreUser: true });
  };

  const likeUser = (relationUserId) => {
    performUserAction({ relationUserId, likeUser: true });
  };

  const unignoreUser = (relationUserId) => {
    performUserAction({ relationUserId, ignoreUser: false });
  };

  const unlikeUser = (relationUserId) => {
    performUserAction({ relationUserId, likeUser: false });
  };

  return {
    ignoreUser,
    likeUser,
    unignoreUser,
    unlikeUser,
    isLoading,
    isError,
    isSuccess,
    data,
    error,
  };
};

export default useUserActions;
