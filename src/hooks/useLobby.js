import {
  // useGetLobbyGamesQuery,
  useGetSubCategoryGamesQuery,
} from '@/reactQuery/gamesQuery';
import useAuthStore from '@/store/useAuthStore';

export default function useLobby({ name, userId }) {
  const { isAuthenticated } = useAuthStore((state) => state);
  const {
    data: lobbyGames,
    isLoading: isLobbyGamesLoading,
    isSuccess,
  } = useGetSubCategoryGamesQuery({
    enabled: isAuthenticated,
    name,
    userId,
  });
  return {
    lobbyGames,
    isLobbyGamesLoading,
    isSuccess,
  };
}
