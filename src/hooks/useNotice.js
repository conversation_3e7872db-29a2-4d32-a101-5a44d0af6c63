import { useEffect } from 'react';
import { useNoticeDetailsQuery } from '@/reactQuery/generalQueries';
import useNoticeStore from '@/store/useNoticeStore';
import { walletSocket } from '@/utils/socket';

function useNotice() {
  const { setNoticeDetails, addNotice, isNoticeModalOpen } = useNoticeStore();
  const { data: noticeDetails, refetch } = useNoticeDetailsQuery({
    enabled: true,
  });
  useEffect(() => {
    if (true) {
      walletSocket.connect();
      walletSocket.on('PUBLISH_NOTICE', (data) =>
        addNotice(data?.data?.checkNoticeExist),
      );
    }

    return () => {
      walletSocket.off('PUBLISH_NOTICE');
    };
  }, []);

  useEffect(() => {
    refetch();
  }, [isNoticeModalOpen]);

  useEffect(() => {
    setNoticeDetails(noticeDetails);
  }, [noticeDetails]);

  console.log('noticeDetails', noticeDetails);
}

export default useNotice;
