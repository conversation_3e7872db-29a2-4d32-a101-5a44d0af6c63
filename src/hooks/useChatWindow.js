import { useState, useEffect, useRef, useCallback } from 'react';
import toast from 'react-hot-toast';
import { GiphyFetch } from '@giphy/js-fetch-api';
import {
  useGetGroupChatsQuery,
  useGetPrivateChatsQuery,
  useGetPublicChatsQuery,
  useGetUserTagsQuery,
  useSendPublicMsgMutation,
  useSendTagMsgMutation,
} from '@/reactQuery/chatWindowQuery';
import usePublicChatsStore from '@/store/usePublicChatStore';
import { liveChatsSocket } from '@/utils/socket';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import useAuthStore from '@/store/useAuthStore';
import useGroupChatStore from '@/store/useGroupChatStore';

const gf = new GiphyFetch('Qn2QLQaEOJz3cBebrCe8bDFLWyDnTjCo');

const useChatWindow = (space) => {
  const [message, setMessage] = useState('');
  const [gifMessage, setGifMessage] = useState('');
  const [searchTag, setSearchTag] = useState('');
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [showGifPicker, setShowGifPicker] = useState(false);
  const [section, setSection] = useState('PublicChat');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedSuggestion, setSelectedSuggestion] = useState(0);
  const [taggedUser, setTaggedUser] = useState(null);
  const [newMessagesCount, setNewMessagesCount] = useState(0);
  const [newGroupMessagesCount, setNewGroupMessagesCount] = useState(0);
  const [grabbedChat, setGrabbedChat] = useState(null);
  const { isAuthenticated } = useAuthStore((state) => state);

  const isSocketConnected = useRef(false);
  const groupChatRef = useRef(false);
  const chatContainerRef = useRef(null);
  const prevTopInGroup = useRef(false);
  const prevScrollHeightInGroup = useRef(0);
  const groupChatContainerRef = useRef(null);
  const groupChatInitialLoad = useRef(true);
  const inputRef = useRef(null);
  const suggestionsRef = useRef([]);
  const initialLoad = useRef(true);
  const prevScrollHeight = useRef(0);
  const prevTop = useRef(false);
  const isInitialMount = useRef(true);

  const fetchGifs = (offset) => gf.trending({ offset, limit: 10 });

  const {
    chats: publicChats,
    setChats,
    appendChat,
    updateRainChat,
    updateGrabbedRainChat,
    setLivePlayersCount,
    livePlayersCount,
  } = usePublicChatsStore((state) => state);

  const {
    privateChat,
    setPrivateChat,
    setRecipientUser,
    isPrivateChatOpen,
    userId,
    recipientUser,
  } = usePrivateChatStore((state) => state);

  const {
    groupChat,
    setGroupChat,
    groupId,
    setLivePlayersCountInGroup,
  } = useGroupChatStore((state) => state);

  const {
    data,
    isLoading: isPublicChatsLoading,
    fetchNextPage,
    hasNextPage,
    refetch: refetchPublicChats,
  } = useGetPublicChatsQuery({
    enabled: isAuthenticated && section === 'PublicChat',
    queryKey: ['publicChats', section],
  });

  const { data: privateChatData } = useGetPrivateChatsQuery({
    enabled: !!(isPrivateChatOpen && userId && section === 'PrivateChat'),
    receiverId: userId,
  });

  const {
    data: groupData,
    isLoading: isGroupChatsLoading,
    fetchNextPage: fetchNextGroupPage,
    hasNextPage: hasNextGroupChat,
    refetch: refetchGroupChats,
  } = useGetGroupChatsQuery({
    groupId,
    enabled: !!(isAuthenticated && groupId && section === 'GroupChat'),
  });

  const { data: tagSuggestion } = useGetUserTagsQuery({
    enabled: !!searchTag,
    params: {
      search: searchTag,
    },
  });

  useEffect(() => {
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }
    
    // Only refetch when section changes and we're on PublicChat
    if (section === 'PublicChat') {
      refetchPublicChats();
    } else if (section === 'GroupChat' && groupId) {
      refetchGroupChats();
    }
  }, [section]);

  const sendMsgMutation = useSendPublicMsgMutation({
    onSuccess: (response) => {
      console.log('Message sent successfully:', response);
    },
    onError: (error) => {
      console.log('Error sending message:', error);
    },
  });

  const sendPrivateMsgMutation = useSendPublicMsgMutation({
    onSuccess: (response) => {
      console.log('Message sent successfully:', response);
    },
    onError: (error) => {
      console.log('Error sending message:', error);
    },
  });
  
  const sendGroupMsgMutation = useSendPublicMsgMutation({
    onSuccess: (response) => {
      console.log('Message sent successfully:', response);
    },
    onError: (error) => {
      console.log('Error sending message:', error);
    },
  });

  const tagMutation = useSendTagMsgMutation({
    onSuccess: (response) => {
      console.log('Message sent successfully:', response);
    },
    onError: (error) => {
      console.log('Error sending message:', error);
    },
  });

  const scrollToBottomGroup = (smooth = false) => {
    if (groupChatContainerRef.current) {
      groupChatContainerRef.current.scrollTo({
        top: groupChatContainerRef.current.scrollHeight,
        behavior: smooth ? 'smooth' : 'auto',
      });
    }
  };

  const scrollToBottom = (smooth = false) => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTo({
        top: chatContainerRef.current.scrollHeight,
        behavior: smooth ? 'smooth' : 'auto',
      });
    }
  };
  
  const isScrolledToBottom = () => {
    if (!chatContainerRef.current) return false;
    return (
      chatContainerRef.current.scrollHeight -
        chatContainerRef.current.scrollTop -
        chatContainerRef.current.clientHeight <
      200
    );
  };

  const handleScrollForGroup = useCallback(() => {
    if (!groupChatContainerRef.current) return;
    
    if (groupChatContainerRef.current.scrollTop === 0 && hasNextGroupChat) {
      prevScrollHeightInGroup.current = groupChatContainerRef.current.scrollHeight;
      fetchNextGroupPage();
      prevTopInGroup.current = true;
    } else if (isScrolledToBottomInGroup()) {
      setNewGroupMessagesCount(0);
    }
  }, [fetchNextGroupPage, hasNextGroupChat]);

  // Optimize the data handling effect
  useEffect(() => {
    if (data?.pages && section === 'PublicChat') {
      setChats(data.pages);
      if (data.livePlayersCount !== undefined) {
        setLivePlayersCount(data.livePlayersCount);
      }
      if (initialLoad.current) {
        scrollToBottom();
        initialLoad.current = false;
      }
    }
  }, [data, section, setChats, setLivePlayersCount]);

  useEffect(() => {
    if (groupId && groupData?.pages && section === 'GroupChat') {
      setGroupChat(groupData.pages);
      if (groupData.livePlayersCount !== undefined) {
        setLivePlayersCountInGroup(groupData.livePlayersCount);
      }
      if (groupChatInitialLoad.current) {
        scrollToBottomGroup();
        groupChatInitialLoad.current = false;
      }
    } else if (groupData?.pages?.length === 0 && section === 'GroupChat') {
      setGroupChat(groupData.pages || []);
    }
  }, [groupId, groupData, section, setGroupChat, setLivePlayersCountInGroup]);

  useEffect(() => {
    if (privateChatData?.data && section === 'PrivateChat') {
      setPrivateChat(privateChatData?.data?.chatDetails?.rows);
      setRecipientUser(privateChatData?.data?.chatDetails?.recipientUser);
    }
  }, [privateChatData, section, setPrivateChat, setRecipientUser]);

  // Socket connection management
  useEffect(() => {
    if (!isSocketConnected.current && isAuthenticated) {
      liveChatsSocket.connect();
      isSocketConnected.current = true;

      const handleNewMessage = (newMessage) => {
        console.log(
          '!!!!!!handleNewMessage',
          newMessage,
          newMessage?.data?.message === 'RAIN_COMPLETED',
        );
        if (newMessage?.data?.message === 'RAIN_COMPLETED') {
          updateRainChat(newMessage?.data);
        } else {
          appendChat(newMessage);
        }
      };

      const handleNotification = (notification) => {
        toast.success(`${notification?.data?.taggedBy} tagged you!`);
      };

      // Remove any existing listeners before adding new ones
      liveChatsSocket.off('public_chat');
      liveChatsSocket.on('public_chat', handleNewMessage);

      return () => {
        liveChatsSocket.off('public_chat', handleNewMessage);
        liveChatsSocket.disconnect();
        isSocketConnected.current = false;
      };
    }
  }, [isAuthenticated, appendChat, updateRainChat]); 

  useEffect(() => {
    if (prevTop.current) {
      chatContainerRef.current.scrollTop =
        chatContainerRef.current.scrollHeight - prevScrollHeight.current;
      prevTop.current = false;
    } else if (publicChats && section === 'PublicChat') {
      const wasScrolledToBottom = isScrolledToBottom();
      if (wasScrolledToBottom) {
        scrollToBottom(true);
        setNewMessagesCount(0);
      } else {
        setNewMessagesCount((prev) => prev + 1);
      }
    }
  }, [publicChats, section]);

  const isScrolledToBottomInGroup = () => {
    if (!groupChatContainerRef.current) return false;
    return (
      groupChatContainerRef.current.scrollHeight -
        groupChatContainerRef.current.scrollTop -
        groupChatContainerRef.current.clientHeight <
      200
    );
  };

  useEffect(() => {
    if (prevTopInGroup.current) {
      groupChatContainerRef.current.scrollTop =
        groupChatContainerRef.current.scrollHeight - prevScrollHeightInGroup.current;
      prevTopInGroup.current = false;
    } else if (groupChat && section === 'GroupChat') {
      const wasScrolledToBottom = isScrolledToBottomInGroup();
      if (wasScrolledToBottom) {
        scrollToBottomGroup(true);
        setNewGroupMessagesCount(0);
      } else {
        setNewGroupMessagesCount((prev) => prev + 1);
      }
    }
  }, [groupChat, section]);

  // Scroll to bottom when changing sections
  useEffect(() => {
    if (section === 'PublicChat' && chatContainerRef.current) {
      scrollToBottom();
    } else if (section === 'GroupChat' && groupChatContainerRef.current) {
      scrollToBottomGroup();
    }
  }, [section]);

  // Attach scroll event listener to group chat container
  useEffect(() => {
    const groupChatContainer = groupChatContainerRef.current;
    if (groupChatContainer && section === 'GroupChat') {
      groupChatContainer.addEventListener('scroll', handleScrollForGroup);
      return () => {
        groupChatContainer.removeEventListener('scroll', handleScrollForGroup);
      };
    }
  }, [handleScrollForGroup, section]);

  const handleScroll = useCallback(() => {
    if (!chatContainerRef.current) return;
    
    if (chatContainerRef.current.scrollTop === 0 && hasNextPage) {
      prevScrollHeight.current = chatContainerRef.current.scrollHeight;
      fetchNextPage();
      prevTop.current = true;
    } else if (isScrolledToBottom()) {
      setNewMessagesCount(0);
    }
  }, [fetchNextPage, hasNextPage]);

  // Attach scroll event listener to chat container
  useEffect(() => {
    const chatContainer = chatContainerRef.current;
    if (chatContainer && section === 'PublicChat') {
      chatContainer.addEventListener('scroll', handleScroll);
      return () => {
        chatContainer.removeEventListener('scroll', handleScroll);
      };
    }
  }, [handleScroll, section]);

  // Set section to PrivateChat when opening private chat
  useEffect(() => {
    if (isPrivateChatOpen) {
      setSection('PrivateChat');
    }
  }, [isPrivateChatOpen]);

  // Reset selected suggestion when showing suggestions
  useEffect(() => {
    if (showSuggestions) {
      setSelectedSuggestion(0);
    }
  }, [showSuggestions]);

  const handleInputChange = (e) => {
    const { value } = e.target;
    if (!gifMessage) {
      setMessage(value);
    }

    const findTaggedText = value.split(' ').slice(-1);
    const mentionIndex = findTaggedText[findTaggedText.length - 1].lastIndexOf('@');
    
    if (mentionIndex !== -1) {
      const mentionText = value.slice(mentionIndex + 1).toLowerCase();
      if (mentionText.length > 0) {
        setSearchTag(mentionText);
        setShowSuggestions(true);
      } else {
        setSearchTag('');
        setShowSuggestions(false);
      }
    } else {
      setSearchTag('');
      setShowSuggestions(false);
    }
  };

  const sendMessage = (msg) => {
    sendMsgMutation.mutate({ message: msg });
  };

  const sendPrivateMessage = (msg) => {
    sendPrivateMsgMutation.mutate({
      message: msg,
      isPrivate: true,
      receiverId: `${userId}`,
    });
  };
  
  const sendGroupMessage = (msg) => {
    sendGroupMsgMutation.mutate({
      message: msg,
      groupId: `${groupId}`,
    });
  };

  const sendTagMessage = (userUniqueId) => {
    tagMutation.mutate({ userUniqueId });
  };

  const selectSuggestion = (index) => {
    const mentionIndex = message.lastIndexOf('@');
    const newMessage = `${message.slice(0, mentionIndex + 1) + tagSuggestion[index].username} `;
    setMessage(newMessage);
    setTaggedUser(tagSuggestion[index]);
    setSearchTag('');
    setShowSuggestions(false);
    inputRef.current.focus();
  };
  
  const handleSendMessage = () => {
    if (message.trim() || gifMessage) {
      if (section === 'PublicChat') {
        sendMessage(gifMessage || message);
        if (taggedUser?.uniqueId) sendTagMessage(taggedUser?.uniqueId);
      } else if (section === 'PrivateChat') {
        sendPrivateMessage(gifMessage || message);
      } else if (section === 'GroupChat') {
        sendGroupMessage(gifMessage || message);
      }
      setMessage('');
      setGifMessage(null);
      setTaggedUser(null);
      setShowEmojiPicker(false);
      setShowGifPicker(false);
    }
  };

  const handleKeyDown = (e) => {
    if (showSuggestions) {
      if (e.key === 'ArrowDown') {
        setSelectedSuggestion((prev) => {
          const newIndex = (prev + 1) % tagSuggestion.length;
          suggestionsRef.current[newIndex]?.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest',
          });
          return newIndex;
        });
      } else if (e.key === 'ArrowUp') {
        setSelectedSuggestion((prev) => {
          const newIndex =
            (prev - 1 + tagSuggestion.length) % tagSuggestion.length;
          suggestionsRef.current[newIndex]?.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest',
          });
          return newIndex;
        });
      } else if (e.key === 'Enter') {
        e.preventDefault();
        selectSuggestion(selectedSuggestion);
      }
    } else if (e.key === 'Enter') {
      handleSendMessage();
    }
  };

  const handleEmojiPickerToggle = () => {
    setShowEmojiPicker(!showEmojiPicker);
    setShowGifPicker(false);
  };

  const handleGifPickerToggle = () => {
    setShowGifPicker(!showGifPicker);
    setShowEmojiPicker(false);
  };

  const handleGifSelect = (gif, event) => {
    event.preventDefault();
    const gifUrl = gif.images.fixed_height.url;
    setGifMessage(gifUrl);
    setShowGifPicker(false);
  };

  return {
    message,
    setMessage,
    gifMessage,
    setGifMessage,
    showEmojiPicker,
    setShowEmojiPicker,
    showGifPicker,
    setShowGifPicker,
    section,
    setSection,
    showSuggestions,
    selectedSuggestion,
    publicChats,
    isPublicChatsLoading,
    chatContainerRef,
    inputRef,
    suggestionsRef,
    tagSuggestion,
    handleKeyDown,
    selectSuggestion,
    handleSendMessage,
    privateChat,
    recipientUser,
    handleInputChange,
    handleGifPickerToggle,
    handleEmojiPickerToggle,
    isPrivateChatOpen,
    livePlayersCount,
    newMessagesCount,
    scrollToBottom,
    fetchNextPage,
    handleGifSelect,
    fetchGifs,
    setGrabbedChat,
    grabbedChat,
    updateGrabbedRainChat,
    groupChat,
    groupChatContainerRef
  };
};

export default useChatWindow;