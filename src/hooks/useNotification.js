import { useEffect } from 'react';
import { useNotificationsQuery } from '@/reactQuery/generalQueries';
import useAuthStore from '@/store/useAuthStore';
import useNotificationStore from '@/store/useNoticeStore';

function useNotifications() {
  const { setNotifications, addNotice, showNotificationPopup, page, setPage } =
    useNotificationStore();
  const { isAuthenticated } = useAuthStore();
  const { data: notifications, refetch } = useNotificationsQuery({
    enabled: isAuthenticated,
    page,
  });

  useEffect(() => {
    if (showNotificationPopup) refetch();
  }, [showNotificationPopup, page]);

  useEffect(() => {
    if (notifications) {
      setNotifications(notifications);
    } else {
      setNotifications();
    }
  }, [notifications]);

  return {
    refetch,
  };
}

export default useNotifications;
