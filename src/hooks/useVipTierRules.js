/* eslint-disable import/prefer-default-export */
import { useGetVipTierRulesQuery } from '@/reactQuery/generalQueries';
import useAuthStore from '@/store/useAuthStore';

export const useVipTierRules = () => {
  const { userDetails } = useAuthStore((state) => state);
  const {
    isLoading,
    isError,
    isSuccess,
    data: vipTierRules,
    error,
  } = useGetVipTierRulesQuery({ enabled: true, userId: userDetails?.id });

  return {
    isLoading,
    isError,
    isSuccess,
    vipTiers: vipTierRules,
    userWagerAmount: vipTierRules?.userWagerAmount,
    vipTierRules,
    error,
  };
};
