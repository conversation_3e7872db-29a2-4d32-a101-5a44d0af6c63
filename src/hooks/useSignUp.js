'use client';

import { toast } from 'react-hot-toast';
import { useState } from 'react';
import useAuthStore from '@/store/useAuthStore';
import {
  useDiscordLoginMutation,
  useGoogleLoginMutation,
  useSignUpMutation,
  useTwitchLoginMutation,
} from '@/reactQuery/authQuery';
import { setAccessToken, setLoginToken } from '@/utils/helper';
import { useRouter } from 'next/navigation';
import useModalStore from '@/store/useModalStore';

const useSignUp = () => {
  const [error, setError] = useState(null);
  const setAuthState = useAuthStore((state) => state.setIsAuthenticated);
  const { setUserDetails, setUserWallet, setIsAuthenticated } = useAuthStore(
    (state) => state,
  );
  const { closeModal, openModal, clearModals } = useModalStore(
    (state) => state,
  );
  const router = useRouter();
  const mutation = useSignUpMutation({
    onSuccess: (response) => {
      if (response) {
        setIsAuthenticated(true);
        localStorage.setItem('isAuthenticated', true);
        setUserDetails(response?.data?.user);
        setUserWallet(
          response?.data?.user?.wallets?.find((wallet) => wallet.default),
        );
      }
      setAuthState(true);
      if (response?.data?.accessToken) {
        setAccessToken(response.data.accessToken);
        // setLoginToken(response.data.accessToken);
      }
      toast.success('Sign-up successful!');
      clearModals();
      localStorage.setItem('isAuthenticated', true);
    },
    onError: (error) => {
      const message =
        error.response?.data?.errors?.[0]?.description || 'Failed to sign up';
      setError(message);
      toast.error(message);
    },
  });


  const discordMutation =useDiscordLoginMutation({
    onSuccess:(response)=>{
      console.log(response, ":::::::::discord response");
      if(response?.data?.success){
        setUserDetails(response?.data?.user);
        setAuthState(true);
        if (response?.data?.accessToken) {
          setAccessToken(response.data.accessToken);
          // setLoginToken(response.data.accessToken);
        }
        router.push('/')
        localStorage.setItem('isAuthenticated', true);
        toast.success('Registered Succesfully!');
        clearModals();
      }
    },
    onError:(error) =>{
      console.log(error, "::::::::::::error");
      const message =
      error.response?.data?.errors?.[0]?.description || 'Failed to register with Google.';
      toast.error(message);
      router.push('/')
    }
  })

  const twitchMutation = useTwitchLoginMutation({
    onSuccess:(response)=>{
      console.log(response, ":::::::::twitch response");
      if(response?.data?.success){
        setUserDetails(response?.data?.user);
        setAuthState(true);
        if (response?.data?.accessToken) {
          setAccessToken(response.data.accessToken);
          // setLoginToken(response.data.accessToken);
        }
        router.push('/')
        localStorage.setItem('isAuthenticated', true);
        clearModals();
        toast.success('Registered Succesfully!');
      }
    },
    onError:(error) =>{
      console.log(error, "::::::::::::error");
      const message =
      error.response?.data?.errors?.[0]?.description || 'Failed to register with Google.';
      toast.error(message);
      router.push('/')
    }
  }) 
  const googleMutation = useGoogleLoginMutation({
    onSuccess: (response) => {
      console.log(response, '::::::::::response for google login');
      if (response?.data?.success) {
        setUserDetails(response?.data?.user);
        setAuthState(true);
        if (response?.data?.accessToken) {
          setAccessToken(response.data.accessToken);
          // setLoginToken(response.data.accessToken);
        }
        clearModals();
        router.push('/');
        localStorage.setItem('isAuthenticated', true);
        toast.success('Registered Succesfully!');
      }
    },
    onError: (error) => {
      console.log(error, ' :::google login');
      const message =
        error.response?.data?.errors?.[0]?.description ||
        'Failed to register with Google.';
      toast.error(message);
    },
  });

  const signUp = (authDetails) => {
    mutation.mutate({
      ...authDetails,
      isTermsAccepted: authDetails.acceptTerms,
      // browser: 'Chrome',
      // platform: 'Windows',
    });
  };

  return { signUp, error, isLoading: mutation.isPending, googleMutation, twitchMutation, discordMutation };
};

export default useSignUp;
