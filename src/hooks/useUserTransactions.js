import { useUserTransactions } from '@/reactQuery/generalQueries';

export const useTransactions = ({startDate, endDate,page, limit, purpose}) => {
    
  const {
    data: casinoTransactions,
    isLoading,
    isError,
    error,
  } = useUserTransactions({startDate: startDate, endDate: endDate , page, limit, purpose});

  return {
    casinoTransactions,
    isLoading,
    isError,
    error,
  };
};
