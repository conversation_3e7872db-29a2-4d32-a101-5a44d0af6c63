import { useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { getPlayerDetails } from '@/utils/apiCalls';
import useUserInfoStore from '@/store/useUserInfoStore';

const useUserDetails = () => {
  const { selectedUserId, setUserDetails, setLoading, setError } =
    useUserInfoStore();

  const { data, error, isLoading, refetch } = useQuery({
    queryKey: ['userDetails', selectedUserId],
    queryFn: () => getPlayerDetails({ userId: Number(selectedUserId) }),
    enabled: !!selectedUserId,
    select: (data) => data?.data?.userData || {},
  });

  useEffect(() => {
    setLoading(isLoading);
    if (error) {
      setError(error);
    } else {
      setUserDetails(data);
    }
  }, [data, error, isLoading, setLoading, setUserDetails, setError]);

  return { data, error, isLoading, refetch };
};

export default useUserDetails;
