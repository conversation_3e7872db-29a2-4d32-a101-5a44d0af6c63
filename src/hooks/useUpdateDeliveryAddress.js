'use client';

import { toast } from 'react-hot-toast';
import { useState } from 'react';
import { useUpdateDeliveryAddressMutation } from '@/reactQuery/inventoryQuery';

const useUpdateDeliveryAddress = () => {
  const [error, setError] = useState(null);

  const mutation = useUpdateDeliveryAddressMutation({
    onSuccess: (response) => {
      toast.success('Delivery Address Updated Successfully!');
    },
    onError: (err) => {
      const message =
        err.response?.data?.errors?.[0]?.description || 'Unable to update deli';
      setError(message);
      toast.error(message);
    },
  });

  const updateDeliveryAddress = (
    deliveryAddress1,
    deliveryAddress2,
    city,
    state,
    postalCode,
    orderSessionId,
    makeDefaultAddress = false,
  ) => {
    mutation.mutate({
      deliveryAddress1,
      deliveryAddress2,
      city,
      state,
      postalCode,
      orderSessionId,
      makeDefaultAddress,
    });
  };

  return { updateDeliveryAddress, error, isLoading: mutation.isPending };
};

export default useUpdateDeliveryAddress;
