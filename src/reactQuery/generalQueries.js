import {
  useMutation,
  useQuery,
  useInfiniteQuery,
  useQueryClient,
} from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import {
  getCmsPageDetails,
  getIgnoredUsers,
  getInventories,
  getLivePlayers,
  getNoticeDetails,
  getPlayerDetails,
  userAction,
  claimFauset,
  getAllCmsPages,
  sendTip,
  getTipsTransactions,
  updateWallet,
  getSpinWheelConfiguration,
  getVipTierRules,
  sendRainDrop,
  getRainTransactions,
  grabRainDrop,
  claimUserTask,
  getUserClaimedTasks,
  getUserTasks,
  claimChest,
  getCards,
  getChestAndCardTransactions,
  claimCardBonus,
  getChestAndCardHistory,
  getUserTransactions,
  getBanners,
  updateNotification,
  getNotifications,
  getTransactions,
} from '@/utils/apiCalls';
import useSpinWheelStore from '@/store/useSpinWheelStore';

export const useGetVipTierRulesQuery = ({ enabled = true, userId }) =>
  useQuery({
    queryKey: ['useGetVipTierRulesQuery', userId],
    queryFn: () => getVipTierRules({ userId }),
    select: (data) => data?.data || [],
    refetchOnMount: true,
    staleTime: 0,
    refetchOnWindowFocus: false,
    enabled: enabled && !!userId,
  });

export const useCmsPageDetailsQuery = ({ enabled = true, pageSlug }) =>
  useQuery({
    queryKey: ['cmsPageDetails', pageSlug],
    queryFn: () => getCmsPageDetails({ pageSlug }),
    select: (data) => data?.data?.data || {},
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });

export const useGetAllCmsPagesQuery = ({ enabled = true }) =>
  useQuery({
    queryKey: ['cmsPageDetails'],
    queryFn: () => getAllCmsPages({}),
    select: (data) => data?.data?.data || {},
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });

export const useGetAllLivePlayersQuery = ({ enabled = true }) =>
  useQuery({
    queryKey: ['allLivePlayers'],
    queryFn: () => getLivePlayers(),
    select: (data) => data?.data?.livePlayers || {},
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });

export const useUserActionMutation = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['USER_ACTION'],
    mutationFn: (data) => userAction(data),
    onSuccess,
    onError,
  });

export const useUserDetailsQuery = (userId) =>
  useQuery({
    queryKey: ['userDetails', userId],
    queryFn: () => getPlayerDetails({ actioneeId: userId }),
    enabled: !!userId,
  });

export const useIgnoredUsersQuery = ({ enabled = true }) =>
  useQuery({
    queryKey: ['ignoredUsers'],
    queryFn: () => getIgnoredUsers(),
    select: (data) => data?.data?.ignoredUsers || [],
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });

// export const useInventoriesQuery = ({ search, enabled = true }) =>
//   useQuery({
//     queryKey: ['inventories', search],
//     queryFn: () => getInventories({ search }),
//     select: (data) => data?.data?.allInventories || [],
// refetchOnMount: true,
// refetchOnWindowFocus: false,
// enabled,
//   });

export const useInventoriesQuery = ({ search, enabled = true }) =>
  useInfiniteQuery({
    queryKey: ['inventories', search],
    queryFn: ({ pageParam = 1 }) => {
      return getInventories({ search, pageNo: pageParam, limit: 10 });
    },
    getNextPageParam: (lastPage, allPages) => {
      const morePagesExist = lastPage?.data?.allInventories?.count;
      if (!morePagesExist) return false;
      return allPages.length + 1;
    },
    select: (data) => {
      return {
        pages: data.pages.flatMap((page) => page.data.allInventories.rows),
      };
    },
    refetchOnMount: true,
    refetchOnWindowFocus: false,
  });

export const useClaimFausetBonusMutation = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['FAUSET_BONUS'],
    mutationFn: (data) => claimFauset(data),
    onSuccess,
    onError,
  });

export const useNoticeDetailsQuery = ({ enabled = true }) =>
  useQuery({
    queryKey: ['noticeDetails'],
    queryFn: () => getNoticeDetails(),
    select: (data) => data?.data?.data || [],
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });

export const useBannersQuery = () =>
  useQuery({
    queryKey: ['getBanners'],
    queryFn: () => getBanners(),
    select: (data) => data?.data?.pages || [],
    refetchOnMount: true,
    refetchOnWindowFocus: false,
  });

export const useSendTipMutation = ({ onError }) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ['SEND_TIP'],
    mutationFn: (data) => sendTip(data),
    onSuccess: () => {
      toast.success('Tip sent!');
      queryClient.invalidateQueries(['tipsTransactions']);
    },
    onError,
  });
};

export const useTipsTransactionsMutation = () =>
  useQuery({
    queryKey: ['tipsTransactions'],
    queryFn: () => getTipsTransactions(),
    select: (data) => data?.data?.rows || [],
    refetchOnMount: true,
    refetchOnWindowFocus: true,
  });
export const useGetSpinWheelConfigurationQuery = ({ enabled }) => {
  const { setSpinWheelData } = useSpinWheelStore((state) => state);
  return useQuery({
    queryKey: ['getSpinWheelConfiguration'],
    queryFn: () => {
      return getSpinWheelConfiguration();
    },
    select: (data) => {
      return data?.data || {};
    },
    enabled,
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    onSuccess: (data) => {
      setSpinWheelData(data?.wheelConfiguration);
    },
  });
};

export const useUpdateSpinWheelWallet = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['spinWheelupdateWallet'],
    mutationFn: () => updateWallet(),
    onSuccess,
    onError,
  });
};

export const useGetCardsQuery = ({ enabled = true }) =>
  useQuery({
    queryKey: ['Cards'],
    queryFn: () => getCards(),
    select: (data) => data?.data?.data || {},
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });

export const useClaimChestMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['claimChest'],
    mutationFn: () => claimChest(),
    onSuccess,
    onError,
  });
};

export const useClaimCardBonusMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['claimCardBonus'],
    mutationFn: () => claimCardBonus(),
    onSuccess,
    onError,
  });
};

export const useUserTasksQuery = () =>
  useQuery({
    queryKey: ['userTasks'],
    queryFn: () => getUserTasks(),
    select: (data) => data?.data?.data || [],
    refetchOnMount: true,
    refetchOnWindowFocus: false,
  });

export const useUserClaimedTasksQuery = () =>
  useQuery({
    queryKey: ['userClaimedTasks'],
    queryFn: () => getUserClaimedTasks(),
    select: (data) => data?.data?.data || [],
    refetchOnMount: true,
    refetchOnWindowFocus: false,
  });

export const useClaimTaskMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['claimTask'],
    mutationFn: (data) => claimUserTask(data),
    onSuccess,
    onError,
  });
};
export const useSendRainDropMutation = (options) =>
  useMutation({
    mutationKey: ['SEND_RAIN_DROP'],
    mutationFn: (data) => sendRainDrop(data),
    ...options,
  });

export const useGetRainTransactions = ({
  rainId = null,
  userId,
  enabled = true,
}) => {
  return useQuery({
    queryKey: ['getRainTransactions', rainId, userId],
    queryFn: () => getRainTransactions({ rainId, userId }),
    select: (data) => data?.data || [],
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });
};

export const useGrabRainDropMutation = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['grabRainDrop'],
    mutationFn: (data) => grabRainDrop(data),
    onSuccess,
    onError,
  });

export const useChestAndCardTransactions = ({ enabled = true }) =>
  useQuery({
    queryKey: ['getChestAndCardTransactions'],
    queryFn: () => getChestAndCardTransactions(),
    select: (data) => data?.data || [],
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });

export const useUserTransactions = ({
  startDate,
  endDate,
  page,
  limit,
  purpose,
  enabled = true,
}) =>
  useQuery({
    queryKey: ['getUserTransactions', startDate, endDate, page, limit, purpose],
    queryFn: () =>
      getUserTransactions({ startDate, endDate, page, limit, purpose }),
    select: (data) => data?.data?.casinoTransaction || [],
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
    staleTime: 0,
  });

export const useAllTransactions = ({
  startDate,
  endDate,
  page,
  limit,
  purpose,
  enabled = true,
}) =>
  useQuery({
    queryKey: ['getTransactions', startDate, endDate, page, limit, purpose],
    queryFn: () =>
      getTransactions({ startDate, endDate, page, limit, purpose }),
    select: (data) => data?.data || [],
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
    staleTime: 0,
  });

export const useChestAndCardHistoryQuery = ({ enabled = true }) =>
  useQuery({
    queryKey: ['getChestAndCardHistory'],
    queryFn: () => getChestAndCardHistory(),
    select: (data) => data?.data || [],
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });

export const useUpdateNotificationMutation = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['UPDATE_NOTIFICATION'],
    mutationFn: (data) => updateNotification(data),
    onSuccess,
    onError,
  });

export const useNotificationsQuery = ({ enabled = true, page = 1 }) =>
  useQuery({
    queryKey: ['notificationsDetails'],
    queryFn: () => getNotifications({ page, limit: 15 }),
    select: (data) => data?.data?.notifications || [],
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });
