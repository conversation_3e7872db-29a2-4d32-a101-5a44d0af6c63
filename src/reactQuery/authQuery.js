import { useMutation, useQuery } from '@tanstack/react-query';
import {
  userLogout,
  userSignIn,
  userSignUp,
  getUserProfile,
  userChangePassword,
  uploadProfileImage,
  twoFactorAuth,
  twoFactorVerfiyOtp,
  twoFactorEnable,
  updateUserDetails,
  userForgotPassword,
  getPreferences,
  updatePreferenceDetails,
  getCountries,
  getState,
  updateEmail,
  verifyEmail,
  twoFactorVerfiyLoginOtp,
  getDeliveryCities,
  getGetLabels,
  uploadKycDoc,
  userSocialGoogleLogin,
  userSocialFacebookLogin,
  userSocialTwitchLogin,
  userSocialDiscordLogin,
} from '@/utils/apiCalls';

export const useSignUpMutation = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['SIGN_UP'],
    mutationFn: (data) => userSignUp(data),
    onSuccess,
    onError,
  });

export const useSignInMutation = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['SIGN_IN'],
    mutationFn: (data) => userSignIn(data),
    onSuccess,
    onError,
  });

export const useForgotPasswordMutation = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['FORGOT_PASSWORD'],
    mutationFn: (data) => userForgotPassword(data),
    onSuccess,
    onError,
  });

export const useLogOutMutation = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['LOG_OUT'],
    mutationFn: () => userLogout(),
    onSuccess,
    onError,
  });

export const useUserProfileQuery = ({ enabled }) =>
  useQuery({
    queryKey: ['USER_PROFILE'],
    queryFn: () => getUserProfile(),
    select: (data) => data?.data?.user || {},
    refetchOnMount: true,
    retry: false,
    refetchOnWindowFocus: false,
    enabled,
  });

export const useChangePasswordMutation = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['CHANGES_PASSWORD'],
    mutationFn: (data) => userChangePassword(data),
    onSuccess,
    onError,
  });

export const useUploadProfileImageMutation = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['UPLOAD_PROFILE_IMAGE'],
    mutationFn: (data) => uploadProfileImage(data),
    onSuccess,
    onError,
  });

export const useTwoFactorAuthentication = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['TwoFactorAuthentication'],
    mutationFn: () => twoFactorAuth(),
    onSuccess,
    onError,
  });

export const useTwoFactorVerifyOtp = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['VerfiyOtp'],
    mutationFn: (data) => twoFactorVerfiyOtp(data),
    onSuccess,
    onError,
  });

export const useTwoFactorEnable = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['TwoFactorEnable'],
    mutationFn: (data) => twoFactorEnable(data),
    onSuccess,
    onError,
  });

export const useUpdateProfileMutation = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['UPDATE_USER_DETAILS'],
    mutationFn: (data) => updateUserDetails(data),
    onSuccess,
    onError,
  });

export const usePreferences = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['GET_PREFERENCES'],
    mutationFn: (data) => getPreferences(data),
    onSuccess,
    onError,
  });

export const useGoogleLoginMutation = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['GOOGLE_SIGN_IN'],
    mutationFn: (data) => userSocialGoogleLogin(data),
    onSuccess,
    onError,
  });

  export const useTwitchLoginMutation =({onSuccess, onError}) => useMutation({
    mutationKey: ['TWITCH_SIGN_IN'],
    mutationFn: (data) => userSocialTwitchLogin(data),
    onSuccess,
    onError,
  })

  export const useDiscordLoginMutation =({onSuccess, onError}) => useMutation({
    mutationKey: ['Discord_SIGN_IN'],
    mutationFn: (data) => userSocialDiscordLogin(data),
    onSuccess,
    onError,
  })



export const useFacebookLoginMutation = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['FACEBOOK_SIGN_IN'],
    mutationFn: (data) => userSocialFacebookLogin(data),
    onSuccess,
    onError,
  });

export const useUpdatePreferences = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['UPDATE_PREFERENCES'],
    mutationFn: (data) => updatePreferenceDetails(data),
    onSuccess,
    onError,
  });

export const useCountriesQuery = ({ enabled }) =>
  useQuery({
    queryKey: ['GET_COUNTRIES'],
    queryFn: () => getCountries(),
    select: (data) => data?.data?.countries || {},
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });

export const useStateQuery = ({ enabled, countryCode }) =>
  useQuery({
    queryKey: ['GET_STATE'],
    queryFn: () => getState({ countryCode }),
    select: (data) => data?.data?.data || {},
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });

export const useCityQuery = ({ enabled, countryCode }) =>
  useQuery({
    queryKey: ['GET_CITY'],
    queryFn: () => getDeliveryCities(),
    select: (data) => data?.data?.rows || {},
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });

export const useUpdateEmail = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['UPDATE_EMAIL'],
    mutationFn: (data) => updateEmail(data),
    onSuccess,
    onError,
  });

export const useVerifyEmail = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['VERIFY_EMAIL'],
    mutationFn: (data) => verifyEmail(data),
    onSuccess,
    onError,
  });

export const useTwoFactorVerifyLoginOtp = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['VerfiyOtp'],
    mutationFn: (data) => twoFactorVerfiyLoginOtp(data),
    onSuccess,
    onError,
  });

export const useGetKycLabelsQuery = () =>
  useQuery({
    queryKey: ['GET_KYC_LABELS'],
    queryFn: () => getGetLabels(),
    select: (data) => data?.data?.getAllDocs,
    refetchOnMount: true,
    refetchOnWindowFocus: false,
  });

export const useUloadKycDocMutation = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['UPLOAD_KYC_DOC'],
    mutationFn: (data) => uploadKycDoc(data),
    onSuccess,
    onError,
  });
