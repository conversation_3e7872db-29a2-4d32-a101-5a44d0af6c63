import { useQuery } from '@tanstack/react-query';
import { getAllBets } from '@/utils/apiCalls';

export const useAllBetsQuery = ({ enabled = true, myBets }) =>
  useQuery({
    queryKey: ['allBets', myBets],
    queryFn: () => {
      if (myBets) {
        return getAllBets({ myBets });
      } else {
        return getAllBets();
      }
    },
    select: (data) => data?.data || {},
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });
