import { useInfiniteQuery, useMutation, useQuery } from '@tanstack/react-query';
import {
  createFriendRequest,
  createGroup,
  getAgoraToken,
  getFriends,
  getFriendsRequest,
  getGroupChats,
  getGroupDetails,
  getGroupList,
  getPrivateChat,
  getPublicChats,
  getRecentChat,
  getUserTags,
  sendDeclinedReq,
  sendPublicChatsMsg,
  sendTagMsg,
  unFriendRequest,
  updateFriendRequest,
  updateGroup,
  updateJoinedGroup,
} from '@/utils/apiCalls';

export const useGetPublicChatsQuery = ({ isAuthenticated }) =>
  useInfiniteQuery({
    queryKey: ['GET_PublicChatsQuery', isAuthenticated],
    queryFn: ({ pageParam = 1 }) => getPublicChats({ pageParam }),
    getNextPageParam: (lastPage, allPages) => {
      const morePagesExist = lastPage?.data?.count;
      if (!morePagesExist) return false;
      return allPages.length + 1;
    },
    select: (data) => ({
      pages: data.pages.flatMap((page) => page.data.rows),
      livePlayersCount: data.pages[0]?.data.livePlayersCount,
    }),
    refetchOnMount: true,
    refetchOnWindowFocus: false,
  });

export const useSendPublicMsgMutation = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['SEND_PUBLIC_MSG'],
    mutationFn: (data) => sendPublicChatsMsg(data),
    onSuccess,
    onError,
  });

export const useGetPrivateChatsQuery = ({ enabled, receiverId }) =>
  useQuery({
    queryKey: ['GET_PRIVATE_CHAT_QUERY', receiverId],
    queryFn: () => getPrivateChat({ receiverId }),
    select: (data) => data,
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });

  export const useGetGroupListQuery = ({ enabled, search }) =>
    useInfiniteQuery({
      queryKey: ['GET_GROUP_LIST_QUERY', search],
      queryFn: async ({ pageParam = 1 }) => {
        const params = { search, page: pageParam, limit: 5 };
        return getGroupList(params);
      },
      getNextPageParam: (lastPage, allPages) => {
        // Check if there are groups in the last page
        const lastPageGroups = lastPage?.data?.groups || [];
        
        // If the last page has no groups, don't request more pages
        if (lastPageGroups.length === 0) {
          return undefined;
        }
        
        const currentCount = allPages.reduce(
          (acc, page) => acc + (page?.data?.groups?.length || 0),
          0,
        );
        const totalCount = lastPage?.data?.count || 0;
  
        // Strict comparison to ensure we don't continue fetching if we've reached the total
        if (currentCount >= totalCount) {
          return undefined;
        }
        
        return allPages.length + 1;
      },
      select: (data) => ({
        groups: data.pages.flatMap((page) => page?.data?.groups || []),
        total: data.pages?.[0]?.data?.count || 0,
      }),
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      enabled,
    });

export const useGroupChatMutation = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['GROUP_CREATE'],
    mutationFn: (data) => createGroup(data),
    onSuccess,
    onError,
  });

export const useGetUserTagsQuery = ({ enabled, params }) =>
  useQuery({
    queryKey: ['GET_USER_TAGS_QUERY', params?.search],
    queryFn: () => getUserTags(params),
    select: (data) => data?.data?.rows,
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });

export const useGetRecentChatsQuery = ({ params }) =>
  useQuery({
    queryKey: ['GET_RECENT_CHAT_QUERY', params.search],
    queryFn: () => getRecentChat(params),
    select: (data) => data?.data?.chatDetails,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    enabled: !!params.search,
  });

export const useJoinedGroupMutation = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['jOINED_GROUP'],
    mutationFn: (data) => updateJoinedGroup(data),
    onSuccess,
    onError,
  });

export const useGetGroupDetails = ({ enabled, params }) => {
  return useQuery({
    queryKey: ['GET_GROUP_DETAILS_QUERY', params?.groupId],
    queryFn: () => getGroupDetails(params?.groupId),
    select: (data) => data?.data,
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });
};

export const useGetGroupChatsQuery = ({ groupId, enabled }) =>
  useInfiniteQuery({
    queryKey: ['GET_GROUP_CHATS_QUERY', groupId],
    queryFn: ({ pageParam = 1 }) => getGroupChats({ pageParam, groupId }),
    getNextPageParam: (lastPage, allPages) => {
      const morePagesExist = lastPage?.data?.count;
      if (!morePagesExist) return false;
      return allPages.length + 1;
    },
    select: (data) => ({
      pages: data.pages.flatMap((page) => page.data.rows),
      livePlayersCount: data.pages[0]?.data.livePlayersCount,
    }),
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });

export const useGroupChatUpdateDetailsMutation = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['GROUP_UPDATE'],
    mutationFn: (data) => updateGroup(data),
    onSuccess,
    onError,
  });

export const useGetFriendsListQuery = ({ enabled = true, params }) =>
  useQuery({
    queryKey: ['GET_FRIEND_LIST_QUERY', params?.search],
    queryFn: () => getFriends(params),
    select: (data) => data?.data?.myFriends,
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
    staleTime: 0
  });

export const useGetFriendsRequestListQuery = ({ enabled = true }) =>
  useQuery({
    queryKey: ['GET_FRIENDS_REQUEST_LIST'],
    queryFn: () => getFriendsRequest(),
    select: (data) => data?.data?.allFriendRequests,
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });

export const useCreateFriendsRequest = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['CREATE_FRIENDS_REQUEST'],
    mutationFn: (data) => createFriendRequest(data),
    onSuccess,
    onError,
  });

export const useUpdateFriendsRequest = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['UPDATE_FRIENDS_REQUEST'],
    mutationFn: (data) => updateFriendRequest(data),
    onSuccess,
    onError,
  });

export const useDeclinedCall = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['cancelled_call'],
    mutationFn: (data) => sendDeclinedReq(data),
    onSuccess,
    onError,
  });

export const useGenerateAgoraToken = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['get_agora_token'],
    mutationFn: (data) => getAgoraToken(data),
    onSuccess,
    onError,
  });

export const useUnFriendsRequest = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['UN_FRIENDS_REQUEST'],
    mutationFn: (data) => unFriendRequest(data),
    onSuccess,
    onError,
  });

export const useSendTagMsgMutation = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['SEND_TAG_MSG'],
    mutationFn: (data) => sendTagMsg(data),
    onSuccess,
    onError,
  });
