import { initKYC } from '@/utils/apiCalls';
import { useQuery } from '@tanstack/react-query';

const initKYCQuery = ({
  enabled = false,
  onSuccess = () => {},
  onError = () => {},
}) => {
  return useQuery({
    queryKey: ['kyc', 'initialization'],
    queryFn: initKYC,
    enabled,
    select: (res) => res?.data || {},
    onSuccess,
    onError: (error) => {
      const errorData = error?.response?.data?.errors?.[0];
      onError(errorData);
    },
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    retry: false,
  });
};

export const VeriffyQuery = {
  initKYCQuery,
};

export default VeriffyQuery;
