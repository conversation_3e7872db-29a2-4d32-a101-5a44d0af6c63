import { z } from 'zod';
import * as yup from 'yup';

export const signInSchema = z.object({
  userName: z.string().regex(/^[a-zA-Z\d]{5,12}$/, {
    message:
      'Username must be 5-12 characters long and contain only letters and digits',
  }),
  password: z
    .string({ required_error: 'Password is required' })
    .regex(
      /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*?&]).{8,32}$/,
      'Password must be 8-32 characters long and include at least one uppercase letter, one lowercase letter, one number, and one special character',
    ),
});

export const signUpSchema = z
  .object({
    username: z
      .string()
      .regex(/^[a-zA-Z\d]{5,12}$/, {
        message:
          'Username must be 5-12 characters long and contain only letters and digits',
      })
      .refine((value) => !/\s/.test(value), {
        message: 'Username cannot contain spaces',
      }),
    email: z
      .string({ required_error: 'Email is required' })
      .email('Invalid email address'),
    // firstName: z
    //   .string({ required_error: 'First Name is required' })
    //   .min(1, 'First Name is required')
    //   .max(50, 'First Name must be less than 50 characters'),
    // lastName: z
    //   .string({ required_error: 'Last Name is required' })
    //   .min(1, 'Last Name is required')
    //   .max(50, 'Last Name must be less than 50 characters'),
    password: z
      .string({ required_error: 'Password is required' })
      .regex(
        /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*?&]).{8,32}$/,
        'Password must be 8-32 characters long and include at least one uppercase letter, one lowercase letter, one number, and one special character',
      ),
    // confirmPassword: z
    //   .string({ required_error: 'Confirm Password is required' })
    //   .regex(
    //     /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*?&]).{8,32}$/,
    //     'Password must be 8-32 characters long and include at least one uppercase letter, one lowercase letter, one number, and one special character',
    //   ),
    acceptTerms: z.boolean().refine((value) => value === true, {
      message: 'You must accept the Terms and Conditions',
    }),
    referralCode: z.string().optional(),
    // over18: z.boolean().refine((value) => value === true, {
    //   message: 'You must confirm that you are at least 18 years old',
    // }),
  })
  // .refine((data) => data.confirmPassword === data.password, {
  //   message: "Password Doesn't Match",
  //   path: ['confirmPassword'],
  // });

  export const changePasswordSchema = yup.object().shape({
    currentPassword: yup.string().required('Current password is required'),
    newPassword: yup
      .string()
      .required('New password is required')
      .min(8, 'New password must be at least 8 characters long')
      .matches(
        /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*?&]).{8,32}$/,
        'Password must be 8-32 characters long and include at least one uppercase letter, one lowercase letter, one number, and one special character'
      ),
    confirmNewPassword: yup
      .string()
      .required('Confirm new password is required')
      .oneOf([yup.ref('newPassword')], 'Passwords do not match'),
  });
// .refine((data) => data.newPassword === data.confirmNewPassword, {
//   message: 'Passwords do not match',
//   path: ['confirmNewPassword'],
// });

export const forgotPasswordSchema = z.object({
  email: z.string().email('Invalid email address'),
});

export const kycDocSchema = yup.object().shape({
  file: yup
    .mixed()
    .required('File is required')
    .test(
      'fileSize',
      'File too large',
      (value) => value && value.size <= 10 * 1024 * 1024,
    )
    .test(
      'fileType',
      'Unsupported File Format',
      (value) =>
        value && ['image/jpeg', 'image/png', 'image/jpg'].includes(value.type),
    ),
});
