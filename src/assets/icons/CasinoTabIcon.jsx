// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function CasinoTabIcon(props) {
  return (
    <svg
      width="40"
      height="34"
      viewBox="0 0 40 34"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path opacity="0.4" d="M11.4476 20.4346C19.0968 18.717 23.1397 22.9472 22.8838 28.7847C22.6126 34.9728 17.7634 40 11.4476 40C5.1318 40 0.282681 34.9728 0.01144 28.7847C-0.24442 22.9472 3.79841 18.717 11.4476 20.4346ZM22.9826 20.3382C24.8233 18.9289 25.5556 18.3851 29.0953 19.1811C36.3829 17.5455 40.233 21.5779 39.9891 27.1372C39.7318 33.0357 35.1099 37.8251 29.0953 37.8251C26.6872 37.8344 26.3484 37.0346 24.4685 35.5587C25.7476 33.54 26.4705 31.2303 26.5665 28.8554C26.704 25.7757 25.7637 23.0439 23.9273 21.1647C23.6339 20.8664 23.3181 20.5902 22.9826 20.3382ZM23.1772 0.0913C23.4516 -0.307745 24.8277 0.708807 25.1502 1.07167C25.3049 1.24611 25.3827 1.47369 25.3663 1.70435C25.3499 1.93501 25.2408 2.14984 25.0629 2.30158C24.1566 3.07487 23.297 3.86033 22.4744 4.65398C22.4725 4.66491 22.4732 4.67566 22.4709 4.68659C21.6445 8.56319 24.1016 11.5178 26.4779 14.375C27.3221 15.3451 28.1021 16.3673 28.8131 17.4353C28.0399 17.2886 27.2565 17.1998 26.4696 17.1697C26.0459 16.6154 25.5776 16.051 25.1003 15.477C23.012 12.9661 20.7003 10.1749 20.5417 6.61876C16.9577 10.4648 14.3445 14.4906 12.8119 18.5189C12.3638 18.5749 11.9098 18.6451 11.4475 18.7394C11.2575 18.7006 11.0702 18.6698 10.8825 18.6375C12.4692 14.1115 15.3301 9.59634 19.3418 5.3189C19.7442 4.89364 20.1167 4.44215 20.4569 3.96751C21.3639 2.69015 22.2776 1.3991 23.1772 0.0913ZM25.5172 3.18147C26.3151 3.05537 31.8919 3.41605 34.3253 5.21564C37.2918 7.40945 37.1749 8.2496 38.4034 14.0113C34.9993 13.4307 32.1908 13.2637 28.5209 10.0662C26.5333 8.31804 25.4341 5.7985 25.5172 3.18147Z" fill="white" style={{ fill: 'white', fillOpacity: 1 }} />
    </svg>
  );
}

export default CasinoTabIcon;
