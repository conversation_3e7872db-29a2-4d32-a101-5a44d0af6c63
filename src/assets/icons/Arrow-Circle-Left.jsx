// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function ArrowCircleLeftIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g>
        <path d="M10,20C4.486,20,0,15.516,0,10C0,4.486,4.486,0,10,0c5.516,0,10,4.486,10,10C20,15.516,15.516,20,10,20z M10,1.512c-4.681,0-8.488,3.809-8.488,8.488c0,4.682,3.808,8.488,8.488,8.488c4.68,0,8.488-3.809,8.488-8.488C18.488,5.32,14.682,1.512,10,1.512z" />
        <path d="M14.325,11.62h-4.81l1.322,1.332c0.301,0.302,0.467,0.704,0.467,1.131c0,0.428-0.166,0.83-0.467,1.131c-0.619,0.624-1.628,0.624-2.247,0L4.556,11.15C4.255,10.85,4.09,10.448,4.09,10.02V9.982c0-0.428,0.165-0.828,0.463-1.129l4.038-4.064c0.3-0.304,0.699-0.471,1.124-0.471c0.426,0,0.824,0.167,1.122,0.468c0.302,0.303,0.468,0.704,0.468,1.132c0,0.428-0.166,0.829-0.466,1.132L9.516,8.383h4.81c0.873,0,1.584,0.727,1.584,1.618S15.198,11.62,14.325,11.62z" />
      </g>
    </svg>
  );
}

export default ArrowCircleLeftIcon;
