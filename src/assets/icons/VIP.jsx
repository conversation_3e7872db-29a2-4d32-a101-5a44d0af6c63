// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function VIPIcon(props) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M12.8321 3.4453C12.6466 3.1671 12.3344 3 12 3C11.6657 3 11.3534 3.1671 11.168 3.4453L7.65766 8.71078L2.44725 6.10557C2.10435 5.93412 1.69361 5.9738 1.38987 6.20773C1.08613 6.44165 0.942863 6.82864 1.02105 7.20395L3.35521 18.4079C3.54843 19.3354 4.36582 20 5.31317 20H18.6869C19.6343 20 20.4516 19.3353 20.6449 18.4079L22.979 7.20395C23.0572 6.82864 22.9139 6.44165 22.6102 6.20773C22.3065 5.9738 21.8957 5.93412 21.5528 6.10557L16.3424 8.71078L12.8321 3.4453Z"
        fill="url(#paint0_linear_11001_1551)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_11001_1551"
          x1="12"
          y1="3"
          x2="12"
          y2="20"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            stopColor="#E2BD68"
            style={{
              stopColor: 'color(display-p3 0.8863 0.7412 0.4078)',
              stopOpacity: 1,
            }}
          />
          <stop
            offset="0.5"
            stopColor="#ECD782"
            style={{
              stopColor: 'color(display-p3 0.9255 0.8431 0.5098)',
              stopOpacity: 1,
            }}
          />
          <stop
            offset="1"
            stopColor="#B57F44"
            style={{
              stopColor: 'color(display-p3 0.7098 0.4980 0.2667)',
              stopOpacity: 1,
            }}
          />
        </linearGradient>
      </defs>
    </svg>
  );
}

export default VIPIcon;
