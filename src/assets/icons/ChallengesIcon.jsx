// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function ChallengesIcon(props) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8 2C6.69378 2 5.58254 2.83481 5.17071 4H5C3.34315 4 2 5.34315 2 7C2 9.09706 3.61375 10.8172 5.66717 10.9864C6.65237 13.0719 8.63747 14.5925 11.0039 14.9297V16H9C7.34315 16 6 17.3431 6 19C6 20.6569 7.34315 22 9 22H15C16.6569 22 18 20.6569 18 19C18 17.3431 16.6569 16 15 16H13.0039V14.9286C15.3669 14.5892 17.3487 13.0696 18.3328 10.9864C20.3862 10.8172 22 9.09706 22 7C22 5.34315 20.6569 4 19 4H18.8293C18.4175 2.83481 17.3062 2 16 2H8ZM5 8C5 8.25512 5.01365 8.50705 5.04025 8.7551C4.42032 8.41539 4 7.75678 4 7C4 6.44772 4.44772 6 5 6V8ZM20 7C20 7.75678 19.5797 8.41539 18.9597 8.7551C18.9864 8.50705 19 8.25512 19 8V6C19.5523 6 20 6.44772 20 7Z"
        fill="#C3C4C7"
        style={{
          fill: 'color(display-p3 0.7647 0.7686 0.7804)',
          fillOpacity: 1,
        }}
      />
    </svg>
  );
}

export default ChallengesIcon;
