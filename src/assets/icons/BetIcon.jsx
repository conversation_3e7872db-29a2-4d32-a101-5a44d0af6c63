// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function BetIcon(props) {
  return (
    <svg
      width="25"
      height="24"
      viewBox="0 0 25 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.5 7C3.5 4.79086 5.29086 3 7.5 3H17.5C19.7091 3 21.5 4.79086 21.5 7V17C21.5 19.2091 19.7091 21 17.5 21H7.5C5.29086 21 3.5 19.2091 3.5 17V7ZM8.5 14C8.5 13.4477 8.94772 13 9.5 13H13.5C14.0523 13 14.5 13.4477 14.5 14C14.5 14.5523 14.0523 15 13.5 15H9.5C8.94772 15 8.5 14.5523 8.5 14ZM9.5 9C8.94772 9 8.5 9.44772 8.5 10C8.5 10.5523 8.94772 11 9.5 11H15.5C16.0523 11 16.5 10.5523 16.5 10C16.5 9.44772 16.0523 9 15.5 9H9.5Z"
        fill="#C3C4C7"
        style={{
          fill: 'color(display-p3 0.7647 0.7686 0.7804)',
          fillOpacity: 1,
        }}
      />
    </svg>
  );
}

export default BetIcon;
