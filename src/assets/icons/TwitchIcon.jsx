// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function TwitchIcon(props) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="17"
      viewBox="0 0 16 17"
      {...props}
    >
      <path fill-rule="evenodd" clip-rule="evenodd" d="M10.0833 8.41667H11.75V4.24999H10.0833V8.41667ZM5.91666 8.41667H7.58333V4.24999H5.91666V8.41667ZM14.25 9.50595V1.74998H2.58332V11.75H5.91666V14.211L8.05 11.75H12.0333L14.25 9.50595ZM11.6725 14.25H8.41083L6.23916 16.75H4.24999V14.25H0.083313V2.9833L1.16665 0.083313H15.9167V10.2274L11.6725 14.25Z" fill="white" style={{ "fill": "white", "fillOpacity": "1" }} />
    </svg>
  );
}

export default TwitchIcon;
