// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function CasinoIcon(props) {
  return (
    <svg
      width="25"
      height="24"
      viewBox="0 0 25 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M8.2238 12.2173C12.0484 11.3585 14.0698 13.4736 13.9419 16.3923C13.8063 19.4864 11.3817 22 8.2238 22C5.0659 22 2.64134 19.4864 2.50572 16.3923C2.37779 13.4736 4.39921 11.3585 8.2238 12.2173ZM13.9913 12.1691C14.9117 11.4644 15.2778 11.1925 17.0476 11.5906C20.6915 10.7728 22.6165 12.789 22.4945 15.5686C22.3659 18.5179 20.055 20.9126 17.0476 20.9126C15.8436 20.9172 15.6742 20.5173 14.7342 19.7794C15.3738 18.77 15.7353 17.6151 15.7833 16.4277C15.852 14.8878 15.3818 13.5219 14.4637 12.5823C14.317 12.4332 14.1591 12.2951 13.9913 12.1691ZM14.0886 2.04565C14.2258 1.84613 14.9138 2.3544 15.0751 2.53584C15.1525 2.62305 15.1913 2.73685 15.1832 2.85217C15.175 2.9675 15.1204 3.07492 15.0315 3.15079C14.5783 3.53743 14.1485 3.93017 13.7372 4.32699C13.7363 4.33246 13.7366 4.33783 13.7355 4.3433C13.3223 6.28159 14.5508 7.75891 15.7389 9.18749C16.1611 9.67256 16.5511 10.1837 16.9065 10.7176C16.52 10.6443 16.1283 10.5999 15.7348 10.5848C15.523 10.3077 15.2888 10.0255 15.0501 9.73849C14.006 8.48305 12.8502 7.08743 12.7708 5.30938C10.9788 7.23239 9.67226 9.2453 8.90596 11.2594C8.68192 11.2874 8.45492 11.3225 8.22374 11.3697C8.12874 11.3503 8.03511 11.3349 7.94124 11.3188C8.73461 9.05573 10.1651 6.79817 12.1709 4.65945C12.3721 4.44682 12.5584 4.22107 12.7284 3.98376C13.1819 3.34507 13.6388 2.69955 14.0886 2.04565ZM15.2586 3.59073C15.6575 3.52768 18.4459 3.70802 19.6627 4.60782C21.1459 5.70473 21.0875 6.1248 21.7017 9.00567C19.9996 8.71535 18.5954 8.63183 16.7604 7.03311C15.7666 6.15902 15.217 4.89925 15.2586 3.59073Z"
        fill="#C3C4C7"
        style={{
          fill: 'color(display-p3 0.7647 0.7686 0.7804)',
          fillOpacity: 1,
        }}
      />
    </svg>
  );
}

export default CasinoIcon;
