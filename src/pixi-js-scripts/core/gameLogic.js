import useSpinWheelStore from '@/store/useSpinWheelStore';
import { getSpinWheelindex } from '@/utils/apiCalls';
import * as UI from './gameUI';
import { pixiApp } from './initializePixi.js';
import {
  spinWheelGCSettings,
  spinWheelSCSettings,
  wheelContentValues,
} from '../settings.js';

let spinResult = {};
export const scTextUI = [];
export const gcTextUI = [];
const numberOfDiv = wheelContentValues.sc.length;
let stopPoint;
const maxTime = 600;
let timer = false;
// NOTE: DO NOT CHANGE THESE CONFIGS
let wheelState = 'idle';
let wheelSpeed = 0;
let spinNumber = 0;
let rounds = 0;
const defaultWheelSpeed = 8;
const reversePull = 0.0445;

const points = [];

const startSound = () => {
  useSpinWheelStore.getState().setSpinWheelSound(true);
};

const stopSound = () => {
  useSpinWheelStore.getState().setSpinWheelSound(false);
};

const playJackpotSound = () => {
  useSpinWheelStore.getState().setJackpotSound(true);
  setTimeout(() => useSpinWheelStore.getState().setJackpotSound(false), 3000);
};

const indexMapping = (i) => {
  const index = numberOfDiv - i;
  return index > numberOfDiv - 1 ? index - numberOfDiv : index;
};

function spinWheen(index = undefined) {
  // TODO: SET STOP POINT HERE
  if (index || index === 0) stopPoint = index;
  else stopPoint = Math.floor(Math.random() * numberOfDiv);
  //  counter ++;
  spinNumber = 0;
  rounds = 0;
  wheelState = 'spin';
}

const getSpinWheelResult = () => {
  getSpinWheelindex()
    .then((response) => {
      if (response?.data?.success) {
        const index = response?.data?.index;
        const result = {
          showResult: true,
          index: response?.data?.index,
          gc: response?.data?.wheelConfiguration?.gc,
          sc: response?.data?.wheelConfiguration?.sc,
          bonusActivated: response?.data?.bonusActivated,
        };
        spinWheen(indexMapping(index));
        spinResult = result;
        useSpinWheelStore.getState().setShowClose(false);
      }
    })
    .catch((error) => {
      console.log('*************error', error);
    });
};

for (let i = 0; i < numberOfDiv; i++) {
  points[i] = (360 / numberOfDiv) * i;
}
// let counter = 6 ;
setTimeout(() => {
  for (let num = 0; num < numberOfDiv; num++) {
    scTextUI[num] = new PIXI.Text(
      wheelContentValues.sc[num],
      spinWheelSCSettings,
    );
    // gameAssets.numberText.push(text)
    scTextUI[num].anchor.set(0.5);
    scTextUI[num].pivot.y = 373;
    scTextUI[num].angle = (360 / numberOfDiv) * num;
    UI.gameAssets.wheel.addChild(scTextUI[num]);
    if (wheelContentValues.sc[num] == 0) {
      scTextUI[num].text = '';
    }

    gcTextUI[num] = new PIXI.Text(
      wheelContentValues.gc[num],
      spinWheelGCSettings,
    );
    // gameAssets.numberText.push(text)
    gcTextUI[num].anchor.set(0.5);
    gcTextUI[num].pivot.y = 510;
    gcTextUI[num].angle = (360 / numberOfDiv) * num;
    UI.gameAssets.wheel.addChild(gcTextUI[num]);
    if (wheelContentValues.gc[num] == 0) {
      gcTextUI[num].text = '';
    }
  }
}, 50);

function spinSuspend() {
  // arrange();
  wheelSpeed = 0;
  wheelState = 'suspended';
}

// function arrange(){
//   const firstValueGC = wheelContentValues.gc[0]
//   const firstValueSC = wheelContentValues.sc[0]
//   console.log(firstValueGC);
//   console.log(firstValueSC);
//   wheelContentValues.gc = wheelContentValues.gc.splice(0,1);
//   wheelContentValues.sc = wheelContentValues.sc.splice(0,1);
//   wheelContentValues.gc.push(firstValueSC);
//   wheelContentValues.sc.push(firstValueSC);
//   }

// Use this to reset the wheel
function resetWheel() {
  UI.gameAssets.wheel.alpha = 1;
  UI.gameAssets.wheel.interactive = true;
  wheelState = 'idle';
}

setTimeout(() => {
  UI.gameAssets.button.on('pointerdown', () => {
    if (wheelState == 'idle') {
      spinSuspend();
      startSound();
      getSpinWheelResult();
    }
  });
}, 100);

// Callback function for result when the wheel stops
function wheelStop() {
  useSpinWheelStore.getState().setSpinWheelResult(spinResult);
  useSpinWheelStore.getState().setShowClose(true);
}
function showReward() {
  timer = true;
}
function timerCallback() {
  wheelState = 'invisible';
  wheelStop();
}

let elapsedTime = 0;

const startGameTicker = () => {
  pixiApp.app.ticker.add((delta) => {
    if (rounds >= 2) {
      rounds = 0;
      wheelState = 'stopping';
    }
    if (wheelState == 'idle') {
      if (UI.gameAssets.wheel.angle <= -360) {
        UI.gameAssets.wheel.angle = 0;
      } else UI.gameAssets.wheel.angle -= 0.2 * delta;
    } else if (wheelState == 'suspended') {
      if (UI.gameAssets.wheel.angle >= 360) {
        spinNumber += 1;
        UI.gameAssets.wheel.angle = 0;
      } else {
        if (wheelSpeed >= defaultWheelSpeed) {
          wheelSpeed = defaultWheelSpeed;
        } else wheelSpeed += 0.2 * delta;
        UI.gameAssets.wheel.angle += wheelSpeed * delta;
      }
    } else if (wheelState == 'spin') {
      if (UI.gameAssets.wheel.angle >= 360) {
        spinNumber += 1;
        UI.gameAssets.wheel.angle = 0;
      } else {
        if (wheelSpeed >= defaultWheelSpeed) {
          wheelSpeed = defaultWheelSpeed;
        } else wheelSpeed += 0.2 * delta;
        UI.gameAssets.wheel.angle += wheelSpeed * delta;
      }
      if (
        UI.gameAssets.wheel.angle >= points[stopPoint] - 5 &&
        UI.gameAssets.wheel.angle <= points[stopPoint] + 5
      ) {
        rounds = spinNumber;
      }
    } else if (wheelState == 'stopping') {
      if (UI.gameAssets.wheel.angle >= 360) UI.gameAssets.wheel.angle = 0;
      else {
        //  elapsed += 0.0000999;
        wheelSpeed -= reversePull * delta;
        UI.gameAssets.wheel.angle += wheelSpeed * delta;
        if (wheelSpeed <= 0.3) {
          wheelSpeed = 0.3;
          if (
            UI.gameAssets.wheel.angle >= points[stopPoint] - 0.5 &&
            UI.gameAssets.wheel.angle <= points[stopPoint] + 0.5
          ) {
            wheelState = 'stop';
            stopSound();
            playJackpotSound();
          }
        }
      }
    } else if (wheelState == 'stop') {
      UI.gameAssets.wheel.angle = points[stopPoint];
      showReward();
    } else if (wheelState == 'invisible') {
      UI.gameAssets.wheel.alpha >= 0
        ? (UI.gameAssets.wheel.alpha -= 0.15 * delta)
        : (wheelState = 'rewardpopUp');
      UI.gameAssets.marker.alpha >= 0
        ? (UI.gameAssets.marker.alpha -= 0.15 * delta)
        : (wheelState = 'rewardpopUp');
    }

    if (timer) {
      elapsedTime += 10 * delta;
      if (elapsedTime >= maxTime) {
        elapsedTime = 0;
        timer = false;
        timerCallback();
        // console.log("time Out");
      }
    }
  });

  pixiApp.app.ticker.start();
};

const stopGameTicker = () => pixiApp.app.ticker.stop();

export { startGameTicker, stopGameTicker };
