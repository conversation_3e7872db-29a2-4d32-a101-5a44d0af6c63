import * as yup from 'yup';

export const updateProfileSchema = yup.object().shape({
  firstName: yup
    .string()
    .matches(/^[A-Za-z]+$/, 'First Name should contain only letters')
    .required('First Name is required'),
  lastName: yup
    .string()
    .matches(/^[A-Za-z]+$/, 'Last Name should contain only letters')
    .required('Last Name is required'),
  username: yup.string().required('Username is required'),
  dateOfBirth: yup.date().required('Date of Birth is required'),
  phoneCode: yup.object().required('Phone Code is required'),
  phone: yup
    .string()
    .matches(/^\d{10}$/, 'Phone Number must be exactly 10 digits')
    .required('Phone Number is required'),
  address: yup
    .string()
    .required('Address is required')
    .matches(/^\S.*$/, 'Address should not contain leading spaces'),
  city: yup
    .string()
    .required('City is required')
    .matches(/^[^\s].*$/, 'City should not contain leading spaces'),
  countryCode: yup
    .object()
    .shape({
      value: yup.string().required('Country is required'),
    })
    .nullable()
    .required('Country is required')
    .typeError('Country is required'),
});

export const schema = yup.object().shape({
  firstName: yup.string().required('First Name is required'),
  lastName: yup.string().required('Last Name is required'),
  username: yup.string().required('Username is required'),
});
