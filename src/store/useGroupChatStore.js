import { create } from 'zustand';
// import { persist } from 'zustand/middleware';

const useGroupChatStore = create(
  // persist(
  (set) => ({
    groupChat: [],
    groupId: null,
    userId: null,
    groupName: '',
    isGroupChatOpen: false,
    livePlayersCountInGroup: 0,
    recipientUser: null,
    isCallActive: false,
    setLivePlayersCountInGroup: (data) => {
      set(() => ({ livePlayersCountInGroup: data }));
    },
    setIsCallActive: (data) => {
      set(() => ({ isCallActive: data }));
    },
    setIsGroupChatOpen: (data) => {
      console.log(data, ':::::::::::::::::::data group');
      if (data) {
        set(() => ({ isGroupChatOpen: data }));
      } else {
        set(() => ({
          groupChat: [],
          userId: null,
          groupId: null,
          isGroupChatOpen: data,
          recipientUser: null,
        }));
      }
    },
    setUserId: (data) => {
      set(() => ({ userId: data }));
    },
    setGroupId: (data) => {
      set(() => ({ groupId: data }));
    },
    setGroupName: (data) => {
      set(() => ({ groupName: data }));
    },
    setRecipientUser: (data) => {
      set(() => ({ recipientUser: data }));
    },
    setGroupChat: (data) => {
      set(() => ({ groupChat: data?.slice().reverse() }));
    },
    appendGroupChat: (message) => {
      set((state) => ({ groupChat: [...state.groupChat, message] }));
    },
  }),
  //   {
  //     name: 'useGroupChatStore',
  //   },
  // ),
);

export default useGroupChatStore;
