import { create } from 'zustand';

const useGreenBonusStore = create((set) => ({
  isGreenBonusApplicable: false,
  betAmountToClaimBonus: 0,
  duration: 0,
  houseEdge: 0,
  gameCount: 0,
  totalBetAmountTill: 0,
  playerId: '',

  setGreenBonusData: (data) =>
    set({
      isGreenBonusApplicable: data.isGreenBonusApplicable,
      betAmountToClaimBonus: data.betAmountToClaimBonus,
      duration: data.duration,
      houseEdge: data.houseEdge,
      gameCount: data.gameCount,
      totalBetAmountTill: data.totalBetAmountTill,
      playerId: data.playerId,
    }),
  setTotalBetAmountTill: (data) =>
    set({
      totalBetAmountTill: data.totalBetAmountTill,
    }),
}));

export default useGreenBonusStore;
