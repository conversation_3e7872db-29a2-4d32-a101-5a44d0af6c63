'use client';

import axios from 'axios';
import { getAccessToken, setAccessToken } from './helper';

// Determine API URL based on environment
const getApiUrl = () => {
  if (typeof window !== 'undefined') {
    const isLocalhost = window.location.hostname === 'localhost' ||
                       window.location.hostname === '127.0.0.1';

    if (isLocalhost) {
      return '/api/proxy'; // Use proxy for localhost
    }
  }
  return process.env.NEXT_PUBLIC_API_URL; // Use direct API for production
};

const axiosInstance = axios.create({
  baseURL: getApiUrl(),
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
  },
});

let cancelSource = axios.CancelToken.source();

const cancelAllRequests = () => {
  cancelSource.cancel('Operation canceled due to authentication error');
  cancelSource = axios.CancelToken.source();
};

axiosInstance.interceptors.request.use(
  (config) => {
    const accessToken = getAccessToken();
    if (accessToken) {
      config.headers['accesstoken'] = accessToken;
      config.cancelToken = cancelSource.token;
    }
    return config;
  },
  (error) => Promise.reject(error),
);

axiosInstance.interceptors.response.use(
  (res) => {
    const accessToken = res.headers.get("accessToken") || res.headers.get("Accesstoken");
    if (accessToken) {
      setAccessToken(accessToken);
    }
    return res.data;
  },
  (error) => {
    const status = error.response?.status;
    const errorCode = error?.response?.data?.errors?.[0]?.errorCode;

    if (status === 401 || status === 403 || errorCode === 3007) {
      cancelAllRequests();
      window.dispatchEvent(new Event('logout'));
    }
    if (axios.isCancel(error)) {
      return Promise.reject(error);
    }

    return Promise.reject(error);
  },
);

const makeRequest = async (
  url,
  method,
  data = {},
  params = {},
  headers = { 'Content-Type': 'application/json' },
) => {
  return axiosInstance({ url, method, data, params, headers });
};

const requestHelper = (method) => (url, data, params, headers) =>
  makeRequest(url, method, data, params, headers);

export const getRequest = requestHelper('GET');
export const postRequest = requestHelper('POST');
export const putRequest = requestHelper('PUT');
export const deleteRequest = requestHelper('DELETE');
