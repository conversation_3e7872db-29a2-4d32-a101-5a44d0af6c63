let audioInstance = null;

const useAudioPlayer = () => {
  if (!audioInstance) {
    audioInstance = new Audio('assets/sounds/ringtone_call_phone.mp3');
  }

  const pauseAudio = () => {
    audioInstance.pause();
    audioInstance.currentTime = 0;
  };

  const playAudio = () => {
    audioInstance.currentTime = 0;
    audioInstance.play();
  };

  return { audio: audioInstance, playAudio, pauseAudio };
};

export default useAudioPlayer;
