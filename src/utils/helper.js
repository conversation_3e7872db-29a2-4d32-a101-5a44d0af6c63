import Cookies from 'js-cookie';

export const fixedTo4DecimalPlaces = (floatStr) => {
  const floatValue = parseFloat(floatStr);
  if (Number.isNaN(floatValue)) {
    return '0';
  }

  const [integerPart, decimalPart = ''] = floatValue.toString().split('.');

  const truncatedDecimalPart = decimalPart.slice(0, 4).padEnd(4, '0');

  return `${integerPart}.${truncatedDecimalPart}`;
};

export const validateTextInput = (value) => {
  const regex = /^[a-zA-Z]+$/;
  return value === '' || regex.test(value);
};

export const validateNumberInput = (value) => {
  const regex = /^\d{0,10}$/;
  return value === '' || regex.test(value);
};

export const validateSpace = (value) => {
  const regex = /\s/;
  return regex.test(value);
};

export const validatePassword = (value) => {
  const regex = /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d]{6,12}$/;
  return regex.test(value);
};

export const eighteenYearsAgo = () => {
  const today = new Date();
  return new Date(today.getFullYear() - 18, today.getMonth(), today.getDate());
};

export function formatDateTime(timestamp) {
  const date = new Date(timestamp);
  const today = new Date();

  // Check if the date is today
  const isToday =
    date.getDate() === today.getDate() &&
    date.getMonth() === today.getMonth() &&
    date.getFullYear() === today.getFullYear();

  // Check if the date is yesterday
  const yesterday = new Date();
  yesterday.setDate(today.getDate() - 1);
  const isYesterday =
    date.getDate() === yesterday.getDate() &&
    date.getMonth() === yesterday.getMonth() &&
    date.getFullYear() === yesterday.getFullYear();

  const timeString = date.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: 'numeric',
    hour12: true,
  });

  if (isToday) {
    return `Today, ${timeString}`;
  }
  if (isYesterday) {
    return `Yesterday, ${timeString}`;
  }
  const dateString = date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
  return `${dateString}, ${timeString}`;
}

export const isValidURL = (str) => {
  const pattern = new RegExp(
    '^(https?:\\/\\/)?' + // protocol
      '((([a-zA-Z\\d]([a-zA-Z\\d-]*[a-zA-Z\\d])*)\\.)+[a-zA-Z]{2,}|' + // domain name and extension
      '((\\d{1,3}\\.){3}\\d{1,3}))' + // OR ip (v4) address
      '(\\:\\d+)?' + // port
      '(\\/[-a-zA-Z\\d%_.~+]*)*' + // path
      '(\\?[;&a-zA-Z\\d%_.~+=-]*)?' + // query string
      '(\\#[-a-zA-Z\\d_]*)?$',
    'i', // fragment locator
  );
  return !!pattern.test(str);
};

export const calculateRemainingTime = (claimedAt) => {
  const now = new Date().getTime();
  const targetDate = claimedAt;

  const targetTime = new Date(targetDate);
  // targetTime.setDate(targetTime.getDate() + 1);

  const timeDifference = targetTime - now;

  if (timeDifference <= 0) return { hours: '00', minutes: '00', seconds: '00' };
  let seconds = Math.floor((timeDifference / 1000) % 60);
  let minutes = Math.floor((timeDifference / 1000 / 60) % 60);
  let totalHours = Math.floor(timeDifference / (1000 * 3600));

  if (totalHours < 10) {
    totalHours = `0${totalHours}`;
  }
  if (minutes < 10) {
    minutes = `0${minutes}`;
  }
  if (seconds < 10) {
    seconds = `0${seconds}`;
  }

  return { hours: totalHours, minutes, seconds };
};

export const formatValueWithK = (amount) => {
  let finalAmount;
  if (amount >= 1000000) {
    finalAmount = amount / 1000000;
    return finalAmount % 1 !== 0
      ? `${finalAmount.toFixed(2)}M`
      : `${finalAmount}M`;
  }

  if (amount < 1000) {
    finalAmount = amount;
    return finalAmount % 1 !== 0 ? finalAmount.toFixed(2) : finalAmount;
  }

  finalAmount = amount / 1000;

  return finalAmount % 1 !== 0
    ? `${finalAmount.toFixed(2)}K`
    : `${finalAmount}K`;
};

export const getAllSCValues = (wheelConfiguration) => {
  if (!wheelConfiguration || wheelConfiguration.length === 0) {
    return [];
  }
  return wheelConfiguration.map((wheel) => formatValueWithK(wheel.sc));
};

export const getAllGCValues = (wheelConfiguration) => {
  if (!wheelConfiguration || wheelConfiguration.length === 0) {
    return [];
  }
  return wheelConfiguration.map((wheel) => formatValueWithK(wheel.gc));
};

export const setAccessToken = (token) => {
  // Determine if we're on localhost
  const isLocalhost = typeof window !== 'undefined' &&
    (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1');

  console.log('🍪 Setting token, isLocalhost:', isLocalhost);
  console.log('🍪 Hostname:', typeof window !== 'undefined' ? window.location.hostname : 'SSR');

  if (isLocalhost) {
    // For localhost development - use localhost domain
    console.log('🍪 Setting cookie with localhost domain');
    Cookies.set('accessToken', token, { domain: 'localhost' });
  } else {
    // For production/dev server - use .fansbets.com domain
    console.log('🍪 Setting cookie with .fansbets.com domain');
    Cookies.set('accessToken', token, { domain: '.fansbets.com' });
  }
}

export const removeAccessToken = () => {
  const isLocalhost = typeof window !== 'undefined' &&
    (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1');

  if (isLocalhost) {
    Cookies.remove('accessToken', { domain: 'localhost' });
  } else {
    Cookies.remove('accessToken', { domain: '.fansbets.com' });
  }
}

export const setLoginToken = (token) =>
  window.localStorage.setItem('access-token', token)

export const getAccessToken = () => {
  const token = Cookies.get('accessToken');
  console.log('🔍 Getting token:', !!token);
  console.log('🔍 All cookies:', document.cookie);
  return token;
};
