import { getRequest, postRequest, putRequest } from './axios';

// GET REQUESTS
export const getUserProfile = (params) => getRequest('/user/get-user', params);
export const getState = (params) => getRequest('/details/state', {}, params);
export const getDeliveryCities = (params) =>
  getRequest('/inventory/city', {}, params);
export const getAllBets = (params) =>
  getRequest('/casino/get-transactions', {}, params);
export const getFriends = (params) => getRequest('/friend/my', {}, params);
export const getGroupDetails = (gId) =>
  getRequest(`live-chat/chat-group?groupId=${gId}`);
export const getAllCmsPages = (param) => getRequest('/details/cms', {}, param);
export const getIgnoredUsers = (params) =>
  getRequest('/user/ignored', {}, params);
export const getPublicChats = ({ pageParam = 1, limit = 20 }) =>
  getRequest(`/live-chat/chat?page=${pageParam}&limit=${limit}`);
export const getPrivateChat = (params) =>
  getRequest('/live-chat/private-chat', {}, params);
export const getGroupList = (params) =>
  getRequest('/live-chat/chat-group', {}, params);
export const getUserTags = (params) =>
  getRequest('/user/tag-users', {}, params);
export const getRecentChat = (params) =>
  getRequest('/live-chat/get-recent-chat', {}, params);
export const getCategoryGames = (params) => {
  return getRequest('/casino/get-all-games', {}, params);
};
export const getNotifications = (params) =>
  getRequest('/live-chat/notification', {}, params);
export const getCmsPageDetails = (param) =>
  getRequest('/details/cms-detail', {}, param);
export const getFavoritesGames = (params) =>
  getRequest('/casino/get-favorite-games  ', params);
export const getCountries = (params) =>
  getRequest('/common/get-countries', params);
export const getPreferences = (params) =>
  getRequest('/user/preferences', params);
export const getFriendsRequest = (params) =>
  getRequest('/friend/requests', params);
export const getLivePlayers = (params) =>
  getRequest('user/live-players', {}, params);
export const getPlayerDetails = (params) =>
  getRequest('user/details', {}, params);
export const getNoticeDetails = (params) =>
  getRequest('/details/notice-detail', {}, params);
export const getInventories = ({ pageNo, limit, search }) =>
  getRequest(`/inventory?page=${pageNo}&limit=${limit}&search=${search}`);
export const getCart = (params) => getRequest('/inventory/cart', {}, params);
export const getGetLabels = () => getRequest('/kyc/get-labels');
export const getSpinWheelConfiguration = () =>
  getRequest('/spinWheelConfiguration/getList');
export const getBanners = () => getRequest('/common/get-banners');
export const getSpinWheelResult = () =>
  getRequest('spinWheelConfiguration/generateIndex');
export const updateWallet = () =>
  getRequest('spinWheelConfiguration/updateWallet');
export const getSpinWheelindex = () =>
  getRequest('spinWheelConfiguration/generateIndex');

export const getAllOrders = (params) =>
  getRequest('/inventory/my-orders', {}, params);

export const getTipsTransactions = () => {
  return getRequest('/user/tip', { limit: 10, page: 1 });
};

export const getVipTierRules = (params) => {
  return getRequest('/vip-tier', {}, params);
};

export const getRainTransactions = (params) => {
  return getRequest('/user/get-rain-transaction?limit=10&page=1', {}, params);
};

export const getCards = () => {
  return getRequest('/chest-and-card');
};

export const getChestAndCardTransactions = () => {
  return getRequest('/chest-and-card/transactions');
};

export const getUserTransactions = (params) => {
  return getRequest('/casino/get-transactions', {}, params);
};

export const getTransactions = (params) => {
  return getRequest('/user/get-transactions', {}, params);
};

export const getGroupChats = ({ pageParam = 1, limit = 20, groupId }) =>
  getRequest(
    `/live-chat/chat?groupId=${groupId}&page=${pageParam}&limit=${limit}`,
  );

export const getChestAndCardHistory = () => {
  return getRequest('/chest-and-card/history');
};

export const getSubCategories = ({ name, userId }) => {
  return getRequest('/casino/get-all-games', {}, { name, userId });
};
export const getFeaturedGames = () => {
  return getRequest('/casino-games/featured-games');
};
export const getUserTasks = () => {
  return getRequest('/user/task');
};
export const getUserClaimedTasks = () => {
  return getRequest('/user/claimed-task');
};
export const initKYC = () => getRequest('/kyc/init-kyc');

// POST REQUESTS
export const userSocialGoogleLogin = (data) =>
  postRequest('/user/googleLogin', data);
export const userSocialFacebookLogin = (data) =>
  postRequest('/user/facebookLogin', data);
export const userSocialDiscordLogin = (data) =>
  postRequest('/user/discord', data);
export const userSocialTwitchLogin = (data) =>
  postRequest('/user/twitch', data);
export const userSignIn = (data) => postRequest('/user/login', data);
export const userLogout = () => postRequest('user/logout');
export const userSignUp = (data) => postRequest('/user/signup', data);
export const sendPublicChatsMsg = (data) =>
  postRequest('/live-chat/chat', data);
export const sendTagMsg = (data) => postRequest('/user/tag-users', data);
export const userForgotPassword = (data) =>
  postRequest('user/forgetPassword', data);
export const gameLaunch = (data) => postRequest('/casino/init-game', data);
export const updateFavorite = (data) =>
  postRequest('/casino/toggle-favorite-game', data);
export const twoFactorAuth = (data) =>
  postRequest('/user/generate-otp-2fa', data);
export const twoFactorVerfiyOtp = (data) =>
  postRequest('/user/verify-otp-2fa', data);
export const twoFactorEnable = (data) =>
  postRequest('/user/disable-auth', data);
export const updateEmail = (data) => postRequest('/user/update-email', data);
export const verifyEmail = (data) => postRequest('/user/verifyEmail', data);
export const createFriendRequest = (data) =>
  postRequest('/friend/requests', data);
export const claimFauset = (data) => postRequest('/bonus/faucet-bonus', data);
export const twoFactorVerfiyLoginOtp = (data) =>
  postRequest('/user/verify-otp-2fa-login', data);
export const addToCart = (data) => postRequest('/inventory/cart', data);
export const uploadKycDoc = (data) =>
  postRequest(
    '/kyc/upload-image',
    data,
    {},
    {
      'Content-Type': 'multipart/form-data',
    },
  );
export const claimGreenBonus = (data) =>
  postRequest('/bonus/claim-green-bonus', data);

export const createGroup = (data) => postRequest('/live-chat/chat-group', data);

export const sendTip = (data) => postRequest('/user/tip', data);

export const sendRainDrop = (data) => postRequest('/user/rain-drop', data);

export const grabRainDrop = (data) => postRequest('/user/grab-rain-drop', data);
export const userChangePassword = (data) =>
  postRequest('/internal/update-password', data);
export const claimChest = (data) => postRequest('/chest-and-card/claim', data);
export const claimCardBonus = (data) =>
  postRequest('/chest-and-card/claim-bonus', data);
export const updateUserDetails = (data) => postRequest('/user/update', data);
export const uploadProfileImage = (data) => {
  return postRequest(
    '/user/upload-profile-image',
    data,
    {},
    {
      'Content-Type': 'multipart/form-data',
    },
  );
};

export const sendDeclinedReq = (data) =>
  postRequest('/agora/decline-call', data);
export const getAgoraToken = (data) =>
  postRequest('/agora/generate-token', data);

// PUT REQUESTS
export const userAction = (data) => putRequest('/user/action', data);
export const updateJoinedGroup = (data) =>
  putRequest('/live-chat/chat-group/update-members', data);
export const updatePreferenceDetails = (data) =>
  putRequest('user/preferences', data);
export const updateGroup = (data) => putRequest('/live-chat/chat-group', data);
export const updateFriendRequest = (data) =>
  putRequest('/friend/requests', data);
export const updateNotification = (data) =>
  putRequest('/user/notification', data);
export const unFriendRequest = (data) => putRequest('/friend/unfriend', data);

export const updateCart = (data) => putRequest('/inventory/cart', data);
export const updateDeliveryAddress = (data) =>
  putRequest('/inventory/update-cart-address');
export const confirmOrder = (data) =>
  putRequest('/inventory/confirm-order', data);
export const updateCartAddress = (data) =>
  putRequest('/inventory/update-cart-address', data);
export const cancelOrder = (data) =>
  putRequest('/inventory/cancel-order', data);

export const claimUserTask = (data) => {
  return putRequest('/user/task', data);
};

// DELETE REQUESTS
