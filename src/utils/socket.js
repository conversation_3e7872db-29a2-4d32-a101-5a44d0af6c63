import { io } from 'socket.io-client';

const URL = process.env.NEXT_PUBLIC_SOCKET_URL;
export const walletSocket = io(`${URL}/private`, {
  transports: ['websocket'],
  // auth:{ token },
  path: '/api/socket',
  withCredentials: true,
  autoConnect: false,
  multiplex: true,
  reconnection: false, 
});

export const livePlayerSocket = io(`${URL}/live-players`, {
  transports: ['websocket'],
  withCredentials: true,
  autoConnect: false,
  reconnection: false, 
});

export const liveBetsSocket = io(`${URL}/live-bets`, {
  transports: ['websocket'],
  withCredentials: true,
  autoConnect: false,
  reconnection: false, 
});

export const myBetsSocket = io(`${URL}/my-bets`, {
  transports: ['websocket'],
  withCredentials: true,
  autoConnect: false,
  reconnection: false, 
});

export const liveChatsSocket = io(`${URL}/user-chat`, {
  transports: ['websocket'],
  path: '/api/socket',
  withCredentials: true,
  autoConnect: false,
  reconnection: false, 
});

export const privateChatsSocket = io(`${URL}/private-chat`, {
  transports: ['websocket'],
  withCredentials: true,
  autoConnect: false,
  reconnection: false, 
});

export const playerSocket = io(`${URL}/player`, {
  transports: ['websocket'],
  withCredentials: true,
  autoConnect: false,
  reconnection: false, 
  multiplex: true,
});


export const chatRoomSocket = io(`${URL}/chat-room`, {
  transports: ['websocket'],
  withCredentials: true,
  autoConnect: false,
  reconnection: false, 
  multiplex: true,
  path: '/api/socket',
});

export const voiceCallConnected = io(`${URL}`, {
  transports: ['websocket'],
  withCredentials: true,
  autoConnect: false,
  multiplex: true,
  reconnection: false, 
  // path: '/api/socket',
});

export const groupChatsSocket = io(`${URL}/chat-room`, {
  transports: ['websocket'],
  withCredentials: true,
  autoConnect: false,
  reconnection: false, 
  path: '/api/socket',
});
